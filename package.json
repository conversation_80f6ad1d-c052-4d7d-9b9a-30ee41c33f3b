{"name": "leaderboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build && node scripts/build.js", "preview": "vite preview"}, "dependencies": {"moment": "^2.30.1", "preact": "^10.27.0"}, "devDependencies": {"@preact/preset-vite": "^2.10.2", "@types/node": "^24.3.0", "@vitejs/plugin-legacy": "^7.2.1", "typescript": "~5.8.3", "vite": "^7.1.2"}}