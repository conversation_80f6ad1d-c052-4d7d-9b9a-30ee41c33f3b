body {
    margin: 0;
    padding: 0;
}

#map {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
}
.mapboxgl-ctrl-logo{ display: none !important; }
.mapboxgl-ctrl-bottom-right{ display: none !important; }
#total-members{
    position: absolute;
    top: 0px;
    right: 0px;
    width: 25%;
    background-color: #351781;
    color:white;
    font-family: sans-serif;
    overflow: hidden;
    font-family: 'gotham';
}
.total-members-right{ width: 34%; position: absolute; right: 0px; }
.total-members-right img{ max-width: 100%; }
.total-members-left{ padding: 5%; padding-left:6%; }
.members{font-size:1vw; opacity: .7; }
.total{font-size:3vw; position: relative; left: -2px; }
.in-studio-now{font-size:1vw; opacity: .7; }

@font-face {
    font-family: gotham;
    src: url(/assets/fonts/Gotham-Bold.eot);
    src: url(/assets/fonts/Gotham-Bold.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Bold.woff2) format("woff2"), url(assets/fonts/Gotham-Bold.woff) format("woff"), url(assets/fonts/Gotham-Bold.ttf) format("truetype");
    font-weight: 700;
    font-style: normal
}
@font-face {
    font-family: gotham;
    src: url(/assets/fonts/gotham-medium.eot);
    src: url(/assets/fonts/gotham-medium.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/gotham-medium.woff) format("woff"), url(assets/fonts/gotham-medium.ttf) format("truetype");
    font-weight: 500;
    font-style: normal
}
@font-face {
    font-family: gotham book;
    src: url(/assets/fonts/Gotham-Book.eot);
    src: url(/assets/fonts/Gotham-Book.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Book.woff2) format("woff2"), url(assets/fonts/Gotham-Book.woff) format("woff"), url(assets/fonts/Gotham-Book.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}