/**
 * F45 Studio Map user checkin class
 */

function formatNumber(n) {
    return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

mapboxgl.accessToken = 'pk.eyJ1IjoibG93ZWJsdWUwNTEwIiwiYSI6ImNqdmtlNndtazBlcWIzenBoZ29xaDB0M2wifQ.iL1tCS42DS7QUmhyc9hirA';

locations = [
    {"name": "New York", "latlng": [-74.00565054938768, 40.73123288832318], "zoom": 3},
    {"name": "Sydney", "latlng": [151.20885419132904, -33.871378849084806], "zoom": 3},
    {"name": "London", "latlng": [-0.12219281004266486, 51.488881723069056], "zoom": 3},
    {"name": "Houston", "latlng": [-95.37534182056166, 29.76647923289233], "zoom": 3},
    {"name": "Los Angeles", "latlng": [-118.23432981329691, 34.04550178582891], "zoom": 3}
];
currentLocationIndex = 0;

var user_total_checkins = 0;
var studio_total = 0;
var is_flying = false;

var map = new mapboxgl.Map({
    container: 'map',
    projection: 'globe',
    style: 'mapbox://styles/loweblue0510/cl4a7k1ue001914sytucv23ho/draft',
    center: currentLocation = locations[currentLocationIndex].latlng,
    zoom: 3,
    scrollZoom: false,
    boxZoom: false,
    dragRotate: false,
    dragPan: false,
    keyboard: false,
    doubleClickZoom: false,
    touchZoomRotate: false
})

map.on('load', async () => {

    var geoJsonData = await getUserCheckinGeo();
    var geoJSON = new GeoJSONTerminator();

    map.addSource('earthquakes_users', {
        type: 'geojson',
        data: geoJsonData.users,
        cluster: true,
        clusterMaxZoom: 14, // Max zoom to cluster points on
        clusterRadius: 100 // Radius of each cluster when clustering points (defaults to 50)
    });
    map.addLayer({
        id: 'clusters_users',
        type: 'circle',
        source: 'earthquakes_users',
        filter: ['has', 'point_count'],
        paint: {
            'circle-color': [
                'step',
                ['get', 'point_count'],
                '#3B29AD', 100,
                '#3B29AD', 750,
                '#3B29AD'
            ],
            'circle-radius': [
                'step',
                ['get', 'point_count'],
                20, 100, 30, 750, 40
            ],
            'circle-stroke-width': 15,
            'circle-stroke-color': '#3B29AD',
            'circle-stroke-opacity': 0.3,
        }
    });
    map.addLayer({
        id: 'clusters_users_points',
        type: 'symbol',
        source: 'earthquakes_users',
        filter: ['has', 'point_count'],
        layout: {
            'text-field': '{point_count_abbreviated}',
            'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
            'text-size': 18
        },
        paint: {
            "text-color": "#ffffff"
        }
    });

    map.addLayer({
        'id': 'daynight',
        'type': 'fill',
        'source': {
            'type': 'geojson',
            'data': geoJSON
        },
        'layout': {},
        'paint': {
            'fill-color': '#000',
            'fill-opacity': 0.2
        }
    });

    //start to fly around the globe
    function flyToPlace() {
        is_flying = true;
        map.stop();
        currentLocation = locations[currentLocationIndex];
        map.flyTo({
            center: currentLocation.latlng,
            speed: .1,
            essential: true
        });
        if (currentLocationIndex == (locations.length - 1)) {
            currentLocationIndex = 0;
        } else {
            currentLocationIndex++;
        }
        is_flying = false;
    }

    flyToPlace();
    map.on('moveend', () => {
        if (!is_flying) {
            setTimeout(flyToPlace, 3000);
        }
    });

    var geoJsonDataInterval = setInterval(async () => {
        var geoJsonData = await getUserCheckinGeo(geoJsonDataInterval);
        map.getSource('earthquakes_users').setData(geoJsonData.users);
    }, 5 * 60 * 1000);

    async function getUserCheckinGeo(geoJsonDataInterval) {
        try {
            var response = await fetch(
                'https://booking.api.f45training.com/v1/studio/total_user_workout',
                {method: 'GET'}
            );
            var resData = await response.json();
            var userCheckInData = resData.data;

            var userGeoJsonData = {
                "type": "FeatureCollection",
                "features": []
            }
            var studioGeoJsonData = {
                "type": "FeatureCollection",
                "features": []
            }

            user_total_checkins = 0;
            userCheckInData.forEach(checkin => {

                user_total_checkins += checkin.total_user_workout;

                if (checkin.total_user_workout > 0) {

                    for (var i = 0; i < checkin.total_user_workout; i++) {
                        userGeoJsonData.features.push({
                            "type": "Feature",
                            "geometry": {
                                "type": "Point",
                                "coordinates": [checkin.longitude, checkin.latitude]
                            },
                            "properties": {
                                "title": "User Checkin",
                                "description": "User Checkin",
                                "marker-color": "#3B29AD",
                                "marker-size": "large",
                                "marker-symbol": "circle"
                            }
                        });
                    }
                }

            });

            document.getElementById("user_total").innerHTML = formatNumber(user_total_checkins);

            return {
                'studios': studioGeoJsonData,
                'users': userGeoJsonData
            };
        } catch (error) {
            console.log(error);
        }
    }

});