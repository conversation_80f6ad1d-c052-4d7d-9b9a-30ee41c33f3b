deploy:
	aws --profile f45tan s3 sync leaderboard s3://leaderboards.f45training.com/ --exclude ".DS_Store"  --delete
clean:
	aws --profile f45tan cloudfront create-invalidation --distribution-id EBVC34CTFLIKZ --paths "/*"
	#aws --profile f45tan cloudfront create-invalidation --distribution-id EBVC34CTFLIKZ --paths "/assets/*"
	#aws --profile f45tan cloudfront create-invalidation --distribution-id EBVC34CTFLIKZ --paths "/assets/js/*"
	#aws --profile f45tan cloudfront create-invalidation --distribution-id EBVC34CTFLIKZ --paths "/global-assessment.html"
	#aws --profile f45tan cloudfront create-invalidation --distribution-id EBVC34CTFLIKZ --paths "/assets/css/global.css"
