import fs from 'fs';
import path from 'path';

const srcDir = path.resolve('dist/src/views');
const destDir = path.resolve('dist');

const copyRecursiveSync = (src, dest) => {
  if (!fs.existsSync(src)) {
    console.error('Source folder not found:', src);
    return;
  }

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  fs.readdirSync(src).forEach((file) => {
    const srcFile = path.join(src, file);
    const destFile = path.join(dest, file);

    if (fs.lstatSync(srcFile).isDirectory()) {
      copyRecursiveSync(srcFile, destFile);
    } else {
      fs.copyFileSync(srcFile, destFile);
    }
  });

  console.log(`Copied all files from ${src} to ${dest}`);
};

copyRecursiveSync(srcDir, destDir);

fs.rmSync(srcDir, { recursive: true, force: true });
console.log(`Deleted folder: ${srcDir}`);
