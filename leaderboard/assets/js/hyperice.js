// code support for old device
// dont use let, const
// dont use arrow function
// alert('lionheart.js');

var App = {
  studio_code: null,
  init: function () {
    var vm = this;
    vm.studio_code = vm.getStudioCode();
    Utility.callAPI("https://studio.api.f45training.com/v1/studio?studio_code=" + vm.studio_code + "&with[]=website", null, function (error, response) {
      var link = response && response.data && response.data.studio && response.data.studio.studio_meta && response.data.studio.studio_meta.hyperice_affiliate_url;

      var canvas = document.getElementById("canvas");
      var ctx = canvas.getContext("2d");

      var backgroundImage = new Image();
      backgroundImage.src = "/assets/images/hyperice-bg.jpg";

      backgroundImage.onload = function () {
        ctx.drawImage(backgroundImage, 0, 0, canvas.width, canvas.height);

        var tempDiv = document.createElement("div");
        document.body.appendChild(tempDiv);
        if (!link) {
          return;
        }

        var qr = new QRCode(tempDiv, {
          text: link,
          width: 130,
          height: 130,
          correctLevel: QRCode.CorrectLevel.H
        });

        setTimeout(function () {
          var qrImg = tempDiv.getElementsByTagName("img")[0];
          var qrImage = new Image();
          qrImage.src = qrImg.src;

          qrImage.onload = function () {
            var qrX = 1050;
            var qrY = 500;
            var qrSize = 225;
            ctx.drawImage(qrImage, qrX, qrY, qrSize, qrSize);
            tempDiv.parentNode.removeChild(tempDiv);
          };
        }, 300);
      };
    });
  },
  getStudioCode: function () {
    return Utility.getParamFromUrl('studio_code');
  },
}

$(document).ready(function () {
  try {
    App.init();
  } catch (err) {
    // alert('Something went wrong!');
  }
  $(window).on('resize', function(){
  });
});
