// code support for old device
// dont use let, const
// dont use arrow function
// alert('lionheart.js');

var App = {
    api_key: null,
    api: {
        workouts: "https://studio.api.f45training.com/v1/studio/leaderboard/workout_images",
        weekly_leaderboard: "https://v2.f45connect.com/api/lionheart/analytics/weekly_leaderboard",
    },
    today: null,
    currentWeek: null,
    daysOfWeek: null,
    workouts: null,
    workouts_expired: null,
    studio_code: null,
    studio: null,
    leaderboardData: null,
    init: function () {
        var vm = this;

        vm.studio_code = vm.getStudioCode();
        vm.currentWeek = Utility.getCurrentWeek(Utility.getParamFromUrl('from'));
        vm.daysOfWeek = Utility.getDaysOfWeek(vm.currentWeek.startOfWeek);

        if (vm.studio_code) {
            Utility.callAPI("https://studio.api.f45training.com/v1/studios?page=0&search=CODE&studio_code[]=" + vm.studio_code, null, function (error, response) {
                if (error) {
                    // alert(error.data.message);
                    console.log('No data');
                    Utility.showLeaderboardTable(false);
                } else {
                    vm.studio = response.data && response.data.studios && response.data.studios[0];
                    var is_new_lionheart = vm.studio && vm.studio.studio_meta && vm.studio.studio_meta.is_new_lionheart;
                    // if (is_new_lionheart) {
                    //     return Utility.showLionheartAds(true);
                    // }

                    vm.today = Utility.getCurrentDate();
                    vm.getWorkouts();
                    vm.getLeaderboardData();

                    setInterval(function () {

                        vm.today = Utility.getCurrentDate();
                        vm.getWorkouts();
                        vm.getLeaderboardData();

                    }, 1000 * 60 * 5); // 5 min
                }
            });

        }
    },

    findFrom: function(array, predicate, thisArg) {
        for (var i = 0; i < array.length; i++) {
            if (predicate.call(thisArg, array[i], i, array)) {
                return array[i];
            }
        }
        return undefined;
    },

    next7Days: function (fromDate) {
        var next7Days = [];
        var startDate = moment(fromDate, "YYYY-MM-DD");
        for (var i = 0; i < 7; i++) {
            next7Days.push(
              startDate.clone().add(i, 'days').format('YYYY-MM-DD')
            );
        }
        return next7Days;
    },

    getStudioCode: function () {
        return Utility.getParamFromUrl('studio_code');
    },

    isToday: function (dateString) {
        return this.today === dateString;
    },

    isWorkoutsExpiredExpired: function (){
        if(this.workouts_expired){
            return this.workouts_expired.isBefore(moment());
        }else{
            return false;
        }
    },

    getLeaderboardData: function () {
        var vm = this;

        var url = 'https://api.lionheart.f45.com/v2/studios/' +  vm.studio_code +'/leaderboards/weekly';

        var token = Utility.getParamFromUrl('access_code');
        if (!token) {
            token = Utility.getParamFromUrl('access_token');
        }
        if (!token) {
            // token = vm.api_key;
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return 0;
        }

        var params = {
            "dateFrom": vm.currentWeek.startOfWeek,
            "dateTo": moment(vm.currentWeek.endOfWeek).add(1, 'days').format('YYYY-MM-DD'),
        };

        Utility.callAPI(url, params, function (error, response) {
            if (error) {
                // alert(error.data.message);
                console.log('No data');
                Utility.showLeaderboardTable(false);
            } else {
                vm.leaderboardData = response.data;
                vm.renderUserList(response.data);
            }
        }, null, true);

    },

    getWorkouts: function (){
        var vm = this;

        var params = {
            "from": vm.currentWeek.startOfWeek,
            "to": vm.currentWeek.endOfWeek,
            "studio_code": vm.studio_code,
        }

        if(!vm.workouts || (vm.workouts && vm.isWorkoutsExpiredExpired())){
            Utility.callAPI(vm.api.workouts, params, function (error, response) {
                if (error) {
                    // alert(error.data.message);
                    console.log('No data');
                    Utility.showLeaderboardTable(false);
                } else {
                    if (response.data.workouts) {
                        vm.workouts = Utility.processWorkoutData(response.data.workouts);
                        vm.workouts_expired = moment().add(6, 'hours');
                        vm.renderHeading();
                    }
                }
            }, null);
        }else{
            vm.renderHeading();
        }
    },

    renderHeading: function () {
        var vm = this;

        var topHeadingHtml = '';

        var startOfWeek = Utility.getDateByFormat(vm.currentWeek.startOfWeek);
        var endOfWeek = Utility.getDateByFormat(vm.currentWeek.endOfWeek);

        topHeadingHtml += '<tr class="t-heading" >\n' +
            '              <th colspan="3" class="text-start" >\n' +
            '                <div class="logo d-inline-block ps-5">\n' +
            '                  <div class="title">LIONHEART</div>\n' +
            '                  <div class="d-flex sub-title align-items-center">\n' +
            '                    <div class="flex-grow-1">\n' +
            '                      <span class="red"></span>\n' +
            '                      <span class="white"></span>\n' +
            '                    </div>\n' +
            '                    <div class="date">'+ startOfWeek + ' - ' + endOfWeek + '</div>\n' +
            '                  </div>\n' +
            '                </div>\n' +
            '              </th>';

        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += '<th class="today"><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }else {
                topHeadingHtml += '<th><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }
        }

        // total
        topHeadingHtml += ' <th><img class="leaderboardLogo" width="82" src="" /></th>';
        topHeadingHtml += '</tr>';

        // date-of-week
        topHeadingHtml += '<tr class="heading">' +
            '                   <td width="100">RANK</td>' +
            '                   <td width="90"></td>' +
            '                   <td class="text-start">NAME</td>';

        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            var dateOfWeekString = moment(vm.daysOfWeek[idx], "YYYY-MM-DD").format('ddd');

            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += ' <td class="today">' + dateOfWeekString + '</td>';
            } else {
                topHeadingHtml += ' <td>' + dateOfWeekString + '</td>';
            }
        }

        topHeadingHtml += '<td>TOTAL</td></tr>';
        topHeadingHtml += '</tr>';

        document.getElementById("top-heading").innerHTML = '';
        document.getElementById("top-heading").innerHTML = topHeadingHtml;

    },

    renderUserList: function (leaderboardData) {
        var vm = this;

        if (!leaderboardData || leaderboardData.length <= 0) {
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return;
        }

        // Studio Name
        var studio = leaderboardData.studio;
        if (studio) {
            document.getElementById("studio-name").innerHTML = (studio.name).replace('F45', 'F45 Training');
        }

        // User List
        var leaderboardHtml = '';
        var users = leaderboardData.users;


        if (!users || users.length <= 0) {
            Utility.showLeaderboardTable(false);
            console.log('No data');
            return;
        }

        console.log('Data loaded');
        Utility.showLeaderboardTable(true);


        var nextRank = null;
        var rankCount = 0;

        for (var idx = 0; idx < users.length; idx++) {
            var item = users[idx];
            var itemHtml = '';


            // start row
            itemHtml += '<tr>';

            // rank
            var itemRank = parseInt(item.rank);
            var itemRankDisplay = '';
            if (nextRank !== itemRank) {
                itemRankDisplay = itemRank;
                nextRank = itemRank;
                rankCount = 0;
            }else {
                rankCount++;
            }

            // rank top
            if (itemRank === 1 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-1">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 2 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-2">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 3 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-3">'+ itemRankDisplay +'</div></div></td>';
            } else {
                itemHtml += '<td><div class="td">' + itemRankDisplay + '</div></td>';
            }

            item.display_name = item.name;

            if (item.avatar) {
                itemHtml += '<td>' +
                    '            <div class="td">' +
                    '               <div class="avatar mx-auto">' +
                    '                    <img src="' + Utility.convertToCdnUrl(item.avatar) + '"/>' +
                    '                  </div>' +
                    '            </div>' +
                    '         </td>';
            } else {
                itemHtml += '<td>' +
                    '            <div class="td">' +
                    '               <div class="avatar mx-auto">' + Utility.getAvatarByName(item) + '</div>' +
                    '            </div>' +
                    '         </td>';
            }

            itemHtml +='<td class="text-start user-name"><div class="td">' + Utility.getDisplayName(item) +
              (item.challenge_flair ? '<img class="challengeLogo" width="31" height="31" src="" alt=""/>' : '') +
              (item.lionheart_flair ? '<img class="lionheartLogo" width="31" height="31" src="" alt=""/>' : '') +
              '</div></td>';

            var scoreData = item.data;

            var today = moment(vm.today, "YYYY-MM-DD");

            var scoreExistCount = 0;
            var next7Days = vm.next7Days(vm.currentWeek.startOfWeek);
            for (var jdx = 0; jdx < 7; jdx++) {
                var currentData = vm.findFrom(scoreData, function (item) {
                    return item.date === next7Days[jdx];
                });
                var is_today = vm.isToday(next7Days[jdx]) ? 'class="today"' : '';

                if (!currentData) {
                    if (today.isBefore(next7Days[jdx])) {
                        itemHtml += ' <td ' + is_today + '><div class="td"></div></td>';
                    } else {
                        itemHtml += ' <td ' + is_today + '><div class="td">-</div></td>';
                    }
                    continue;
                }
                if (currentData.point) {
                    scoreExistCount += 1;
                }

                var is_max_score = (currentData.is_top === 1) ? '*' : '';

                if (today.isAfter(currentData.date)) {
                    itemHtml += ' <td ' + is_today + '><div class="td">' + Utility.getNumber(currentData.point) + is_max_score + '</div></td>';
                } else if (today.isSame(currentData.date)) {
                    var point = Utility.getNumber(currentData.point);
                    point = (point !== '-') ? point : '';
                    itemHtml += ' <td ' + is_today + '><div class="td">' + point  + is_max_score + '</div></td>';

                } else {
                    itemHtml += ' <td ' + is_today + '><div class="td"></div></td>';
                }
            }

            itemHtml += ' <td><div class="td">' + Utility.getNumber(item.total) + '</div></td>';

            itemHtml += '</tr>';

            leaderboardHtml += itemHtml;

        }

        leaderboardHtml += Utility.addStudioRows(10 - users.length, vm.daysOfWeek, vm.today);

        document.getElementById("leaderboard").innerHTML = '';
        document.getElementById("leaderboard").innerHTML = leaderboardHtml;

        Utility.setFullScreen();
        Utility.updateLeaderboardAssets();
    }
}

$(document).ready(function () {
    try {
        App.init();
    } catch (err) {
        alert('Something went wrong!');
    }
    $(window).on('resize', function(){
        Utility.setFullScreen();
    });
});
