@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Bold.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Book.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Book.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Medium.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Light.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Bold.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Black.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow Book';
  src: url('../fonts/GothamNarrow-Book.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Book.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Thin.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Light.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Medium.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow Ultra';
  src: url('../fonts/GothamNarrow-Ultra.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Ultra.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

html {
  font-size: 67%;
  background-color: #16142C;
  background: linear-gradient(90deg, #16142C 0%, #1F1D3E 34.9%, #1E1C3B 62.5%, #16142C 100%);

}
html.row-11 {
  font-size: 63%
}
img {
  max-width: 100%;
}
@media (min-width: 1200px) and (max-width: 1400px) {
  html {
    font-size: 47.5%
  }
}
@media(min-width: 2000px) {
  html {
    font-size: 89.4%
  }
}

@media(min-width: 3000px) {
  html {
    font-size: 134%
  }
}

th  img {
  max-width: 8rem;
  width: auto;
}

body {
  background-color: #16142C;
  background: linear-gradient(90deg, #16142C 0%, #1F1D3E 34.9%, #1E1C3B 62.5%, #16142C 100%);
  color: white;
  font-family: 'Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 3rem;
  line-height: 120%;
  font-weight: 500;
}
.td.text-center.ps-0 {
  font-style: normal;
}
.container-fluid  {
  padding: 0 1.5rem 1.5rem;
}

/* Fix for 1280 x 720 */
@media only screen and (max-width: 1300px) {
  /* html {
    font-size: 31%!important
  }
  html.row-11 {
    font-size: 29%!important
  }
  .container-fluid  {
    padding: 2rem 5.5rem 1.5rem;
  } */
  .table-bottom .right {
    font-size: 2.2rem;
  }

}

.table {
  color: inherit;
  text-align: center;
  /* max-width: 99.99%; */
}
.table tr {
  background: linear-gradient(90deg, rgba(156, 156, 164, 0.2) 0%, rgba(163, 163, 163, 0.05) 100%);
  background: rgba(156, 156, 164, 0.2);
}
.table tr.active {
  background: linear-gradient(90deg, #5451F7 0%, rgba(84, 81, 246, 0.05) 100%);
  background: #5451F7;
}
.table tr.first {
  /*background: linear-gradient(90deg, #C9BE5A 0%, rgba(201, 190, 90, 0.05) 100%);*/
  /*background: -webkit-linear-gradient(-45deg, #C9BE5A 0%, rgba(201, 190, 90, 0.05) 100%);*/
  /*background: -webkit-linear-gradient(270deg, #C9BE5A 0%, rgba(201, 190, 90, 0.05) 100%);*/
  background: rgba(201,190,90,.76) ;
  /*background: url("/assets/images/g-1.png") no-repeat center;*/
  /*background-repeat: no-repeat;*/
}
.table tr.first td ,.table tr.secondary td,.table tr.third td{
  background: transparent;
}
.table tr.secondary {
  background: linear-gradient(90deg, #9E9EA9 0%, rgba(158, 158, 169, 0.05) 100%);
  background: #9E9EA9 ;
}
.table tr.third {
  background: linear-gradient(90deg, #A26A3B 0%, rgba(161, 106, 59, 0.05) 100%);
  background: #A26A3B ;
}
.table tr td {
  font-style: italic;
  vertical-align: middle;
  color: #E5E7EB;
  font-weight: 400;
  line-height: 3.6rem;
  font-size: 3rem;
  padding: 1rem 1.8rem;
  height: 6.8rem;
  border: none;
  box-shadow: none!important;
  border-top: .48rem solid #16142C;
  }

.table tr th {
  vertical-align: middle;
  /* font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; */
  font-size: 3.8rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
  border: none;
  text-transform: uppercase;
  text-align: center;
  background-color: transparent;
}
.table tr.t-heading {
  background: transparent;
  /* background: linear-gradient(90deg, #16142C 0%, #1B1A35 51.98%, #16142C 100%); */
}
.table tr td:first-child {
   font-style: normal;
   border-radius: 1.6rem 0 0 1.6rem;
   /* padding-left: 5.8rem; */
   padding-right: 0rem;
}
.col-avatar {
  width: 5.2rem;
}
.table tr td:last-child,.table tr th:last-child {

  border-radius:  0 1.6rem 1.6rem  0;
}
.table .average-row td {

  border-bottom: none;
}
.avatar {
  width: 3.8rem;
  height: 3.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  overflow: hidden;
  color: #1C1949;
  font-size: 2rem;
  font-style: normal;
  font-weight: 400;
  /* margin-right: 5.5rem; */
  /* margin-left: 0.7rem; */
  border-radius: 50%;
}
.avatar-no-image {
  background: #C5C6CB;
}
.avatar-no-image span {
  position: relative;
  top: 1px
}
.table tr td.user-name {
  font-style: normal;
}
.avatar img {
  width: 3.8rem;
  height: 3.8rem;
  border-radius: 50%;
  object-fit: cover;
}

.title {
  font-size: 6.2rem;
  line-height: 120%;
}
.t-heading th {
  text-align: left;
}
.t-heading .date {
  font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 2.6rem;
}

tr.heading td:nth-child(1) {
  width: 14rem;
}
tr.heading td:nth-child(2) {
  width: 10rem;
}
tr.heading td:nth-child(3) {
  width: 42rem;
}

.sub-title span {
  display: block;
  height: .45rem;
  margin-right: 1rem;
  background-color: white;
}

.sub-title .red {
  background: #D6001C;
  height: .8rem;
  margin-bottom: .3rem;
}
.table tr.heading {
  background: transparent;
  border: none;
}
.table tr.heading td {
  background: transparent;
  border: none;
  font-style: normal;
  font-weight: inherit;
  font-size: 3.8rem;
  text-transform: uppercase;
  font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
.today {
  background: rgba(255, 255, 255, 0.1)!important;
  border-bottom-color: #323149!important
}

.table tr:last-child td {
  border-bottom: none;
}
.table-bottom {
  gap: 2rem;
}
.table-bottom .text {
  font-size: 2.2rem;
}
.table-bottom .left {
  max-width: 33.333%;
  flex: 0 0 33.333%;
}
.table-bottom {
  line-height: 1.2;
  font-size: 2.5rem;
  margin-top: 2.4rem;
}
.table-bottom .left img {
  max-width: 6.6rem;
  flex: 0 0 6.6rem;
}
.table-bottom  .middle {
  color: #ABB0B7;
  font-size: 2rem;
  text-align: center;
  font-weight: 300;
  max-width: 40.333%;
  flex: 0 0 40.333%;
}
.table-bottom .right {
  text-align: right;
  text-transform: uppercase;
  flex: 1
}
.col-flag .avatar{
  background-color: transparent;

}

.table tr.active td {
  font-size: 4.2rem;
}
.table.region .avatar {

}
.table tr.active td:first-child {
  font-size: 4rem;
  padding-left: 2.5rem;

}
.table tr.active .user-name {
  font-size: 3.2rem;
}
.table tr.empty td ,.table tr.empty {
  background: none;
  padding-left: 0;
  border: none;

}
.table.region tr.active td .flag img{
  width: 6.4rem;
  height: 6.4rem;
  min-width:6.4rem;
  border-radius: 50%;
}
.table.region tr.active td .avatar {
  width: 6.4rem;
  height: 6.4rem;
  min-width:6.4rem;
  border-radius: 50%;
}
.table.region tr.active td .avatar img{
  width: 6.4rem;
  height: 6.4rem;
  min-width:6.4rem;
  border-radius: 50%;
}
.table.region tr td .flag img {
  min-width: 5.2rem;
  width: 5.2rem;
  background: none;
  height: 3.8rem;
}
.table tr.empty td{
  border-top: none;
  border-bottom: .48rem solid transparent;
  vertical-align: middle;
  line-height: 1;
}
.table tr.empty + tr td{
  border-top:none;
}


.table.region tr.top-1 td {
  font-size: 3.6rem;
}
.table.region tr.top-1 td:first-child {
  font-size: 3rem;
}
.table.region tr.top-1 td.user-name {
  font-size: 3.4rem;
}

#leaderboard_table {
  display: none;
}

.hidden {
  visibility: hidden;
}
