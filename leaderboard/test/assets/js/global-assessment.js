// code support for old device
// dont use let, const
// dont use arrow function
// alert('assessment.js');

var App = {
    api_key: "",
    api: {
        workouts: "https://studio.api.staging.f45training.com/v1/studio/leaderboard/workout_images",
        global_leaderboard: "https://studio.api.staging.f45training.com/v1/assessment/leaderboard/global",
        current_assessment: "https://studio.api.f45training.com/v1/challenge/assessment/meta",
    },
    studio_code: null,
    today: null,
    currentWeek: null,
    daysOfWeek: null,
    gender: null,
    workouts: null,
    workouts_time: {
        start: '',
        end: ''
    },
    workouts_expired: null,
    assessment_ids: null,
    assessment_expired: null,
    event_id: null,
    init: function () {
        var vm = this;

        vm.studio_code = vm.getStudioCode();
        // vm.gender = vm.getGender();
        vm.currentWeek = Utility.getCurrentWeek(Utility.getParamFromUrl('from'));
        vm.event_id = Utility.getParamFromUrl('event_id');
        vm.hide_date = Utility.getParamFromUrl('hide_date');
        if (vm.studio_code) {

            vm.today = Utility.getCurrentDate();
            vm.getAssessment();

            setInterval(function () {

                vm.today = Utility.getCurrentDate();
                vm.getAssessment();

            }, 1000 * 60 * 5);

        }
    },

    getStudioCode: function () {
        return Utility.getParamFromUrl('studio_code');
    },

    isToday: function (dateString) {
        return this.today === dateString;
    },

    isCurrentStudio: function (studio_code){
        return this.studio_code.toLowerCase() === studio_code.toLowerCase();
    },

    isAssessmentExpired: function (){
        if(this.assessment_expired){
            return this.assessment_expired.isBefore(moment());
        }else{
            return false;
        }
    },

    isWorkoutsExpiredExpired: function (){
        if(this.workouts_expired){
            return this.workouts_expired.isBefore(moment());
        }else{
            return false;
        }
    },

    getAssessment: function () {

        var vm = this;


        if (!vm.assessment_ids) {

            var assessment_ids_male = Utility.convertAssessmentIds(Utility.getParamFromUrl('assessment_ids_0'));
            var assessment_ids_female = Utility.convertAssessmentIds(Utility.getParamFromUrl('assessment_ids_1'));

            if(assessment_ids_male && assessment_ids_female){
                assessment_ids_male = Utility.formatAssessmentIds(assessment_ids_male);
                assessment_ids_female = Utility.formatAssessmentIds(assessment_ids_female);
                vm.assessment_ids = Utility.getAssessmentIdsToArray([assessment_ids_male, assessment_ids_female]);
            }

            if(!vm.assessment_ids){
                var assessment_ids = Utility.convertAssessmentIds(Utility.getParamFromUrl('assessment_ids'));
                if(assessment_ids){
                    assessment_ids = Utility.formatAssessmentIds(assessment_ids);
                    vm.assessment_ids = Utility.getAssessmentIdsToArray([assessment_ids]);
                }
            }

            // console.log(vm.assessment_ids);
        }

        if(!vm.assessment_ids || (vm.assessment_ids && vm.isAssessmentExpired())){
            var url = vm.api.current_assessment;
            Utility.callAPI(url, null, function (error, response) {
                if (error) {
                    alert(error.data.message);
                } else {
                    if (response.data.assessment_ids.global_male && response.data.assessment_ids.global_female) {

                        var assessment_ids_male = Utility.formatAssessmentIds(response.data.assessment_ids.global_male);
                        var assessment_ids_female = Utility.formatAssessmentIds(response.data.assessment_ids.global_female);

                        vm.assessment_ids = Utility.getAssessmentIdsToArray([assessment_ids_male, assessment_ids_female]);

                        vm.assessment_expired = moment().add(1, 'hours');
                        vm.getLeaderboardData();
                    }
                }
            }, null);
        }else{
            vm.getLeaderboardData();
        }

    },

    getLeaderboardData: function () {
        var vm = this;

        if (!vm.assessment_ids && !vm.event_id) return;

        vm.getWorkouts();

        var token = Utility.getParamFromUrl('access_code');
        if (!token) {
            token = Utility.getParamFromUrl('access_token');
        }
        if (!token) {
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return 0;
        }

        var params = {
            "from": vm.currentWeek.startOfWeek,
            "to": vm.currentWeek.endOfWeek,
            "studio_code": vm.studio_code,
        }

        if (vm.event_id) {
            params.event_id = vm.event_id;
            delete params.from;
            delete params.to;
        } else {
            if(vm.assessment_ids.length === 1){
                params.assessment_id = vm.assessment_ids[0];
            }else {
                for (var idx = 0; idx < vm.assessment_ids.length; idx++){
                    var assessment_ids_day = vm.assessment_ids[idx];
                    for(var jdx = 0; jdx < assessment_ids_day.length; jdx++){
                        params['assessment_ids[' + idx +'][' + jdx + ']'] = assessment_ids_day[jdx];
                    }
                }
            }
        }

        // console.log(params);
        Utility.callToStudioAPI(vm.api.global_leaderboard, params, function (error, response) {
            if (error) {
                // alert(error.data.message);
                console.log('No data');
                Utility.showLeaderboardTable(false);
            } else {
                vm.renderStudioList(response.data);
            }
        }, token);
    },

    getWorkouts: function (){
        var vm = this;

        var params = {
            "from": vm.currentWeek.startOfWeek,
            "to": vm.currentWeek.endOfWeek,
            "studio_code": vm.studio_code,
        }

        if (vm.event_id) {
            params.event_id = vm.event_id;
            delete params.from;
            delete params.to;
        } else {
            if(vm.assessment_ids.length === 1){
                params.assessment_id = vm.assessment_ids[0];
            }else {
                for (var idx = 0; idx < vm.assessment_ids.length; idx++){
                    var assessment_ids_day = vm.assessment_ids[idx];
                    for(var jdx = 0; jdx < assessment_ids_day.length; jdx++){
                        params['assessment_ids[' + idx +'][' + jdx + ']'] = assessment_ids_day[jdx];
                    }
                }
            }
        }

        if(!vm.workouts || (vm.workouts && vm.isWorkoutsExpiredExpired())){
            Utility.callAPI(vm.api.workouts, params, function (error, response) {
                if (error) {
                    // alert(error.data.message);
                    console.log('No data');
                    Utility.showLeaderboardTable(false);
                } else {
                    if (response.data.workouts) {
                        vm.workouts = Utility.processWorkoutData(response.data.workouts);
                        vm.workouts_time = {
                            start: response.data.from,
                            end: response.data.to,
                        }
                        vm.daysOfWeek = response.data.workouts.map( function(w){
                            return w.date;
                        });
                        vm.workouts_expired = moment().add(6, 'hours');
                        vm.renderHeading();
                    }
                }
            }, null);
        }else{
            vm.renderHeading();
        }
    },

    renderHeading: function () {

        var vm = this;

        if (!vm.workouts) return;

        // var gender = '';
        // if(vm.gender){
        //     gender = ' • ' + vm.gender;
        // }

        var topHeadingHtml = '';
        var startTime = Utility.getDateByFormat(vm.workouts_time.start);
        var endTime = Utility.getDateByFormat(vm.workouts_time.end);

        topHeadingHtml += '<tr class="t-heading" >\n' +
          '              <th colspan="3" class="text-start" >\n' +
          '                <div class="logo d-inline-block ps-5">\n' +
          '                  <div class="title">ASSESSMENTS</div>\n' +
          '                  <div class="d-flex sub-title align-items-center">\n' +
          '                    <div class="flex-grow-1">\n' +
          '                      <span class="red"></span>\n' +
          '                      <span class="white"></span>\n' +
          '                    </div>\n' +
          '                    <div class="date">'+ startTime + ' - ' + endTime + '</div>\n' +
          '                  </div>\n' +
          '                </div>\n' +
          '              </th>';


        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += '<th class="today"><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }else {
                topHeadingHtml += '<th><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }
        }

        // total
        topHeadingHtml += ' <th><img class="leaderboardLogo" width="82" src="" /></th>';
        topHeadingHtml += '</tr>';


        // date-of-week
        topHeadingHtml += '<tr class="heading">' +
          '                   <td width="100">RANK</td>' +
          '                   <td width="90"></td>' +
          '                   <td class="text-start">NAME</td>';

        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            if (vm.hide_date === '1') {
                topHeadingHtml += ' <td></td>';
                continue;
            }
            var dateOfWeekString = moment(vm.daysOfWeek[idx], "YYYY-MM-DD").format('ddd');

            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += ' <td class="today">' + dateOfWeekString + '</td>';
            } else {
                topHeadingHtml += ' <td>' + dateOfWeekString + '</td>';
            }
        }

        topHeadingHtml += '<td>SUM</td></tr>';
        topHeadingHtml += '</tr>';

        document.getElementById("top-heading").innerHTML = '';
        document.getElementById("top-heading").innerHTML = topHeadingHtml;
        Utility.updateLeaderboardAssets();

    },
    renderStudioList: function (leaderboardData) {
        var vm = this;

        if (!leaderboardData || leaderboardData.length <= 0) return;

        // Studios List
        var leaderboardHtml = '';
        var studios = leaderboardData.studios;

        if (!studios || studios.length <= 0) {
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return;
        }

        // Utility.showLeaderboardTable(true);

        // console.log('Data loaded');

        if (studios.length <= 0) return;

        var currentIndex = null;

        // find rank of current studio for render
        for (var idx = 0; idx < studios.length; idx++) {
            if(vm.isCurrentStudio(studios[idx].code)){
                currentIndex = studios[idx].index;
                if(studios[idx].total_score > 0){
                    console.log('Data loaded');
                    Utility.showLeaderboardTable(true);
                }else {
                    console.log('No data');
                    Utility.showLeaderboardTable(false);
                }
                break;
            }
        }

        // not found current studio in response data
        if(currentIndex === null){
            console.log('Data loaded');
            Utility.showLeaderboardTable(true);
            // return;
        }

        if(currentIndex > 10){
            Utility.addClassToHtml('row-11');
        }else {
            Utility.removeClassToHtml('row-11');
        }

        var rankCount = 0;
        var nextRank = null;

        for (var idx = 0; idx < studios.length; idx++) {
            var item = studios[idx];
            var itemHtml = '';

            // display empty row if studio no have data
            if(item.total_score <= 0){
                leaderboardHtml += Utility.addStudioRows(1, vm.daysOfWeek, vm.today);
                continue;
            }

            if(idx === 3 && currentIndex > 10){

                itemHtml += Utility.addPaddingRow(vm.daysOfWeek, vm.today);
            }

            if(vm.isCurrentStudio(item.code)){
                itemHtml += '<tr class="current">';
            }else {
                itemHtml += '<tr>';
            }

            // rank
            var itemRank = parseInt(item.rank);
            var itemRankDisplay = '';
            if (nextRank !== itemRank) {
                itemRankDisplay = itemRank;
                nextRank = itemRank;
                rankCount = 0;
            }else {
                rankCount++;
            }
            // rank top
            if (itemRank === 1 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-1">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 2 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-2">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 3 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-3">'+ itemRankDisplay +'</div></div></td>';
            } else {
                itemHtml += '<td><div class="td">' + itemRankDisplay + '</div></td>';
            }


            // country flag
            itemHtml += '<td>' +
              '            <div class="td">' +
              '               <div class="avatar mx-auto">' +
              '                    <img src="/assets/images/flags/' + (item.country_iso_code).toLowerCase() + '.png"/>' +
              '                  </div>' +
              '            </div>' +
              '         </td>';

            itemHtml +='<td class="text-start user-name"><div class="td">' + item.name + '</div></td>';

            var scoreData = item.score;

            var today = moment(vm.today, "YYYY-MM-DD");

            for (var jdx = 0; jdx < scoreData.length; jdx++) {

                var is_today = vm.isToday(scoreData[jdx].date) ? 'class="today"' : '';
                if (today.isSame(scoreData[jdx].date)) {
                    var point = Utility.getNumber(scoreData[jdx].score);
                    point = (point !== '-') ? point : '';
                    itemHtml += ' <td ' + is_today + '><div class="td">' + point.toLocaleString() + '</div></td>';
                } else {
                    itemHtml += ' <td ' + is_today + '><div class="td">' + (scoreData[jdx].score ? Utility.getNumber(scoreData[jdx].score).toLocaleString() : '') + '</div></td>';
                }

            }

            itemHtml += ' <td><div class="td">' + Utility.getNumber(item.total_score).toLocaleString() + '</div></td>';


            itemHtml += '</tr>';

            leaderboardHtml += itemHtml;
        }

        leaderboardHtml += Utility.addStudioRows(10 - studios.length, vm.daysOfWeek, vm.today);

        document.getElementById("leaderboard").innerHTML = '';
        document.getElementById("leaderboard").innerHTML = leaderboardHtml;

        Utility.setFullScreen();

    },
}

$(document).ready(function () {

    try {
        App.init();
    } catch (err) {
        // alert(err);
        console.log(err);
        alert('Something went wrong!!');
    }

    $(window).on('resize', function(){
        Utility.setFullScreen();
    });

});
