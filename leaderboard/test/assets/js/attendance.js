// code support for old device
// dont use let
// dont use arrow function
// alert('attendance.js');

var App = {
    api_key: "",
    api: {
        workouts: "https://studio.api.f45training.com/v1/studio/leaderboard/workout_images",
        attendance: "https://studio.api.f45training.com/v2/challenge/leaderboard/attendance",
    },
    today: null,
    currentWeek: null,
    daysOfWeek: null,
    studio_code: null,
    workouts: null,
    workouts_expired: null,
    init: function () {
        var vm = this;

        vm.studio_code = vm.getStudioCode();
        vm.currentWeek = Utility.getCurrentWeek(Utility.getParamFromUrl('from'));
        vm.daysOfWeek = Utility.getDaysOfWeek(vm.currentWeek.startOfWeek);

        if (vm.studio_code) {

            vm.today = Utility.getCurrentDate();
            vm.getWorkouts();
            vm.getLeaderboardData();

            setInterval(function () {

                vm.today = Utility.getCurrentDate();
                vm.getWorkouts();
                vm.getLeaderboardData();

            }, 1000 * 60 * 5);
        }
    },

    getStudioCode: function () {
        return Utility.getParamFromUrl('studio_code');
    },

    isToday: function (dateString) {
        return this.today === dateString;
    },

    getLeaderboardData: function () {
        var vm = this;

        var url = vm.api.attendance;

        var token = Utility.getParamFromUrl('access_code');
        if (!token) {
            token = Utility.getParamFromUrl('access_token');
        }
        if (!token) {
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return 0;
        }

        var params = {
            "from": vm.currentWeek.startOfWeek,
            "to": vm.currentWeek.endOfWeek,
            "studio_code": vm.studio_code,
        }

        Utility.callToStudioAPI(url, params, function (error, response) {
            if (error) {
                // alert(error.data.message);
                console.log('No data');
                Utility.showLeaderboardTable(false);
            } else {
                vm.renderUserList(response.data);
            }
        }, token);
    },

    isWorkoutsExpiredExpired: function (){
        if(this.workouts_expired){
            return this.workouts_expired.isBefore(moment());
        }else{
            return false;
        }
    },

    getWorkouts: function (){
        var vm = this;

        var params = {
            "from": vm.currentWeek.startOfWeek,
            "to": vm.currentWeek.endOfWeek,
            "studio_code": vm.studio_code,
        }

        if(!vm.workouts || (vm.workouts && vm.isWorkoutsExpiredExpired())){
            Utility.callAPI(vm.api.workouts, params, function (error, response) {
                if (error) {
                    // alert(error.data.message);
                    console.log('No data');
                    Utility.showLeaderboardTable(false);
                } else {
                    if (response.data.workouts) {
                        vm.workouts = Utility.processWorkoutData(response.data.workouts);
                        vm.workouts_expired = moment().add(6, 'hours');
                        vm.renderHeading();
                    }
                }
            }, null);
        }else{
            vm.renderHeading();
        }
    },

    renderHeading: function () {
        var vm = this;

        var topHeadingHtml = '';

        var startOfWeek = Utility.getDateByFormat(vm.currentWeek.startOfWeek);
        var endOfWeek = Utility.getDateByFormat(vm.currentWeek.endOfWeek);

        topHeadingHtml += '<tr class="t-heading" >\n' +
            '              <th colspan="3" class="text-start" >\n' +
            '                <div class="logo d-inline-block ps-5">\n' +
            '                  <div class="title">ATTENDANCE</div>\n' +
            '                  <div class="d-flex sub-title align-items-center">\n' +
            '                    <div class="flex-grow-1">\n' +
            '                      <span class="red"></span>\n' +
            '                      <span class="white"></span>\n' +
            '                    </div>\n' +
            '                    <div class="date">'+ startOfWeek + ' - ' + endOfWeek + '</div>\n' +
            '                  </div>\n' +
            '                </div>\n' +
            '              </th>';

        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += '<th class="today"><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }else {
                topHeadingHtml += '<th><img width="88" src="' + vm.workouts[vm.daysOfWeek[idx]] + '" />' +'</th>';
            }
        }

        // total
        topHeadingHtml += ' <th><img class="leaderboardLogo" width="82" src="" /></th>';
        topHeadingHtml += '</tr>';

        // date-of-week
        topHeadingHtml += '<tr class="heading">' +
            '                   <td width="100">RANK</td>' +
            '                   <td width="90"></td>' +
            '                   <td class="text-start">NAME</td>';

        for (var idx = 0; idx < vm.daysOfWeek.length; idx++) {
            var dateOfWeekString = moment(vm.daysOfWeek[idx], "YYYY-MM-DD").format('ddd');

            if (vm.isToday(vm.daysOfWeek[idx])) {
                topHeadingHtml += ' <td class="today">' + dateOfWeekString + '</td>';
            } else {
                topHeadingHtml += ' <td>' + dateOfWeekString + '</td>';
            }
        }

        topHeadingHtml += '<td>TOTAL</td></tr>';
        topHeadingHtml += '</tr>';

        document.getElementById("top-heading").innerHTML = '';
        document.getElementById("top-heading").innerHTML = topHeadingHtml;
    },

    renderUserList: function (leaderboardData) {
        var vm = this;

        if (!leaderboardData || leaderboardData.length <= 0) {
            console.log('No data');
            Utility.showLeaderboardTable(false);
            return;
        }

        // Studio Name
        var studio = leaderboardData.studio;

        if (studio) {
            document.getElementById("studio-name").innerHTML = (studio.name).replace('F45', 'F45 Training');
        }

        // User List
        var leaderboardHtml = '';
        var users = leaderboardData.user;

        if (!users || users.length <= 0) {
            Utility.showLeaderboardTable(false);
            console.log('No data');
            return;
        }

        console.log('Data loaded');
        Utility.showLeaderboardTable(true);

        var nextRank = null;
        var rankCount = 0;

        for (var idx = 0; idx < users.length; idx++) {
            var item = users[idx];
            var itemHtml = '';

            // start row
            itemHtml += '<tr>';

            // rank
            var itemRank = parseInt(item.rank);
            var itemRankDisplay = '';
            if (nextRank !== itemRank) {
                itemRankDisplay = itemRank;
                nextRank = itemRank;
                rankCount = 0;
            }else {
                rankCount++;
            }

            // rank top
            if (itemRank === 1 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-1">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 2 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-2">'+ itemRankDisplay +'</div></div></td>';
            } else if (itemRank === 3 && rankCount === 0) {
                itemHtml += '<td><div class="td"><div class="td-3">'+ itemRankDisplay +'</div></div></td>';
            } else {
                itemHtml += '<td><div class="td">' + itemRankDisplay + '</div></td>';
            }

            if (item.avatar) {
                itemHtml += '<td>' +
                    '            <div class="td">' +
                    '               <div class="avatar mx-auto">' +
                    '                    <img src="' + Utility.convertToCdnUrl(item.avatar) + '"/>' +
                    '                  </div>' +
                    '            </div>' +
                    '         </td>';
            } else {
                itemHtml += '<td>' +
                    '            <div class="td">' +
                    '               <div class="avatar mx-auto">' + Utility.getAvatarByName(item) + '</div>' +
                    '            </div>' +
                    '         </td>';
            }

            itemHtml +='<td class="text-start user-name"><div class="td">' + Utility.getDisplayName(item) +
              (item.challenge_flair ? '<img class="challengeLogo" width="31" height="31" src="" alt=""/>' : '') +
              (item.lionheart_flair ? '<img class="lionheartLogo" width="31" height="31" src="" alt=""/>' : '') +
              '</div></td>';

            var scoreData = item.score;

            var today = moment(vm.today, "YYYY-MM-DD");

            for (var jdx = 0; jdx < scoreData.length; jdx++) {

                var is_max_score = (scoreData[jdx].is_max_score === 1) ? '*' : '';

                var is_today = vm.isToday(scoreData[jdx].date) ? 'class="today"' : '';

                if (today.isAfter(scoreData[jdx].date)) {
                    itemHtml += ' <td ' + is_today + '><div class="td">' + Utility.getNumber(scoreData[jdx].score) + is_max_score + '</div></td>';
                } else if (today.isSame(scoreData[jdx].date)) {
                    var point = Utility.getNumber(scoreData[jdx].score);
                    point = (point !== '-') ? point : '';
                    itemHtml += ' <td ' + is_today + '><div class="td">' + point  + is_max_score + '</div></td>';

                } else {
                    itemHtml += ' <td ' + is_today + '><div class="td"></div></td>';
                }
            }

            itemHtml += ' <td><div class="td">' + Utility.getNumber(item.total_score) + '</div></td>';

            itemHtml += '</tr>';

            leaderboardHtml += itemHtml;

        }

        leaderboardHtml += Utility.addStudioRows(10 - users.length, vm.daysOfWeek, vm.today);

        document.getElementById("leaderboard").innerHTML = '';
        document.getElementById("leaderboard").innerHTML = leaderboardHtml;

        Utility.setFullScreen();
        Utility.updateLeaderboardAssets();
    }
}

$(document).ready(function () {

    try {
        App.init();
    } catch (err) {
        alert(err);
        alert('Something went wrong!');
    }

    $(window).on('resize', function(){
        Utility.setFullScreen();
    });

});
