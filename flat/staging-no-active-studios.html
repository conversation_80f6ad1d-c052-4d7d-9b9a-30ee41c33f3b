<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='utf-8'/>
    <title>Studio Map - The League week</title>

    <meta name='viewport' content='initial-scale=1,maximum-scale=1,user-scalable=no'/>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet">

        <script src='https://api.mapbox.com/mapbox.js/v3.3.1/mapbox.js'></script>
    <link href='https://api.mapbox.com/mapbox.js/v3.3.1/mapbox.css' rel='stylesheet'/>
    <link href='assets/css/main-staging.css' rel='stylesheet'/>

    <script src='https://api.mapbox.com/mapbox.js/plugins/leaflet-markercluster/v1.0.0/leaflet.markercluster.js'></script>
    <script src="https://unpkg.com/@joergdietrich/leaflet.terminator@1.0.0/L.Terminator.js"></script>
    <link href='https://api.mapbox.com/mapbox.js/plugins/leaflet-markercluster/v1.0.0/MarkerCluster.css' rel='stylesheet' />
    <link href='https://api.mapbox.com/mapbox.js/plugins/leaflet-markercluster/v1.0.0/MarkerCluster.Default.css' rel='stylesheet' />

    <link rel="icon" href="assets/images/f45-logo.svg" sizes="32x32"/>
    <link rel="icon" href="assets/images/f45-logo.svg" sizes="192x192"/>
    <link rel="apple-touch-icon" href="assets/images/f45-logo.svg"/>
    <meta name="msapplication-TileImage" content="assets/images/f45-logo.svg"/>

</head>
<body>

<div id='map'></div>
<div class="total-navbar" id="total-members">
    <div class="navbar-layout">
        <img class="logo" src="assets/images/f45-logo-small.svg" />
        <div class="total-col-layout">
            <div class="total-col-info">
                <div class="heading-col">TRAINING NOW</div>
                <div class="list-item">
<!--                    <div class="item-info">-->
<!--                        <div id="studioTotal" class="total">0</div>-->
<!--                        <div class="label">STUDIOS</div>-->
<!--                    </div>-->
                    <div class="item-info">
                        <div id="userTotal" class="total">0</div>
                        <div class="label">MEMBERS</div>
                    </div>
                </div>
            </div>
            <div class="total-col-info">
                <div class="heading-col">GLOBAL VISITS</div>
                <div class="list-item">
                    <div class="item-info">
                        <div id="globalVisitsTotal" class="total">0</div>
                        <div class="label">THIS WEEK</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="legend">
    <div class="legend__inner">
        <span>MEMBERS</span>
    </div>
</div>

<script type="text/javascript" src='assets/js/main-flatmap-staging-no-active-studios.js'></script>

</body>
</html>
