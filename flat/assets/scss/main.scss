:root {
  --body-font-family: 'gotham', Helvetica, Arial, sans-serif;
  --heading-font-family: 'gotham book', Helvetica, Arial, sans-serif;
  --primary-color: #3B29AD;
  --secondary-color: #d6001c;
  --secondary-color-darken: #F11212;
  --bg-darken: #1C2430;
  --bg-dark: #303947;
}

@keyframes changeColorSecondary {
  0% {
    background-color: var(--secondary-color);
  }
  70% {
    background-color: var(--secondary-color);
  }
  100% {
    background-color: var(--secondary-color-darken);
  }
}

// FONT FACE
@font-face {
  font-family: gotham;
  src: url(/assets/fonts/Gotham-Bold.eot);
  src: url(/assets/fonts/Gotham-Bold.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Bold.woff2) format("woff2"), url(assets/fonts/Gotham-Bold.woff) format("woff"), url(assets/fonts/Gotham-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal
}

@font-face {
  font-family: gotham;
  src: url(/assets/fonts/gotham-medium.eot);
  src: url(/assets/fonts/gotham-medium.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/gotham-medium.woff) format("woff"), url(assets/fonts/gotham-medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal
}

@font-face {
  font-family: 'gotham book';
  src: url(/assets/fonts/Gotham-Book.eot);
  src: url(/assets/fonts/Gotham-Book.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Book.woff2) format("woff2"), url(assets/fonts/Gotham-Book.woff) format("woff"), url(assets/fonts/Gotham-Book.ttf) format("truetype");
  font-weight: 400;
  font-style: normal
}

// COMMON STYLES
body {
  margin: 0;
  padding: 0;
  font-family: var(--body-font-family);
  font-size: 14px;
  font-weight: 500;
}

#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}

.mapboxgl-ctrl-logo {
  display: none !important;
}

.mapboxgl-ctrl-bottom-right {
  display: none !important;
}

.total-navbar {
  position: absolute;
  top: 0px;
  left: 0;
  width: 100%;
  color: white;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.navbar-layout {
  background-color: var(--bg-dark);
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;

  > * {
    flex: 1 0 0%;
  }
}

.logo {
  object-fit: cover;
  min-width: 96px;
  //.wrap-img {
  //  img {
  //    max-width: 100%;
  //    height: 100%;
  //    display: block;
  //    object-fit: cover;
  //  }
  //}
}

.total-col-info {
  display: flex;
  flex-direction: column;
  text-align: center;

  + .total-col-info {
    border-left: 2px solid rgba(255, 255, 255, .2);
  }

  .heading-col {
    padding: 10px;
    background-color: var(--bg-darken);
    font-family: var(--heading-font-family);
    letter-spacing: 1px;
    font-size: 12px;
    opacity: .8;
  }
  .list-item {
    display: flex;
  }
  .item-info {
    padding: 15px 40px;
    text-align: center;
  }

  .label {
    color: rgb(255 255 255 / 80%);
    white-space: nowrap;
    font-size: 9px;
    margin-top: 5px;
    display: inline-block;
  }

  .total {
    font-size: 32px;
    line-height: 1;
    position: relative;
    left: -2px;
    white-space: nowrap;

    small {
      font-weight: 400;
      font-size: 20px;
    }
  }
}

.mapbox-logo {
  display: none !important;
}

/* marker cluster */
#map {
  .marker-cluster {
    background-color: rgba(59, 41, 173, 0.4);
    border-radius: 50%;
    z-index: 10000 !important;
    &-very-large {
      span {
        font-size: 16px;
      }
    }

    &-large {
      span {
        font-size: 15px;
      }
    }

    &-medium {
      span {
        font-size: 14px;
      }
    }

    &-small {
      span {
        font-size: 13px;
        padding-left: 1px;
      }
    }

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(59, 41, 173, 0.9);
      border-radius: 50%;
      margin-left: 15%;
      margin-top: 15%;
      width: 70%;
      height: 70%;
      font-family: var(--body-font-family);
    }

    span {
      color: #fff;
      line-height: 1;
      padding-top: 2px;
    }
  }
}
/* end marker cluster */

.legend {
  position: absolute;
  left: 50px;
  bottom: 50px;
  background-color: red;
  width: 100px;
  height: 100px;
  background-color: rgba(59, 41, 173, 0.4);
  border-radius: 50%;

  &__inner {
    margin-left: 10%;
    margin-top: 10%;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background-color: rgba(59, 41, 173, 0.9);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
  }
}

