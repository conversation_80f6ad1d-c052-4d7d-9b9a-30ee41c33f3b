/**
 * F45 Studio Map user checkin class
 */
function formatNumber(n) {
  return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// L.mapbox.accessToken = 'pk.eyJ1IjoibG93ZWJsdWUwNTEwIiwiYSI6ImNqdmtlNndtazBlcWIzenBoZ29xaDB0M2wifQ.iL1tCS42DS7QUmhyc9hirA';
L.mapbox.accessToken = 'pk.eyJ1IjoiZGV2ZWxvcGVyLW9mZnNwcmluZyIsImEiOiJjbDRod3dwYW8wNDIxM2JwZzJpanIzaW4yIn0.tPEdNYAWgg4LdO6Law3Zvw';


var user_total_checkins = 0;
var markerCluster = new L.MarkerClusterGroup();
var currentMarkers = [];
var map = L.mapbox.map('map', null,
  {
    zoomControl: false,
    attributionControl: false,
    minZoom: 2,
    maxZoom: 18,
    worldCopyJump: true,
    zoomSnap: 0.1,
    // maxBounds: [ [-90, -180], [90, 180] ],
  }).setView([30, 4], 2.3);
L.control.zoom = false;
// L.mapbox.styleLayer('mapbox://styles/loweblue0510/cl4a7k1ue001914sytucv23ho/draft').addTo(map);
L.mapbox.styleLayer('mapbox://styles/developer-offspring/cl4hwx2bm004815jk7l6f9p55', {
  // continuousWorld: false,
  // noWrap: true,
  // bounds: [ [-90, -180], [90, 180] ]
}).addTo(map);

getUserCheckinGeo(true);
setInterval(getUserCheckinGeo, 5 * 60 * 1000);

function getUserCheckinGeo(isFirstExecution) {
  console.log("getting user checkin data");
  try {
    httpGetAsync('https://booking.api.f45training.com/v1/studio/total_user_workout', function (response) {
      var jsonResponse = JSON.parse(response);
      var userCheckInData = jsonResponse.data;
      var user_total_checkins = 0;
      var total_active_studios = 0;
      var total_workout_week = 0;
      // remove current markers
      if (currentMarkers !== null) {
        for (var i = currentMarkers.length - 1; i >= 0; i--) {
          currentMarkers[i].remove();
        }
        currentMarkers = [];
      }
      map.removeLayer(markerCluster);
      markerCluster = new L.MarkerClusterGroup({
        // showCoverageOnHover: false,
        iconCreateFunction: function (cluster) {
          var iconSize;
          var className = ' marker-cluster-';
          var childCount = cluster.getChildCount();
          if (childCount >= 1000) {
            className += 'very-large';
            iconSize = new L.Point(90, 90);
          } else if (childCount < 1000 && childCount >= 100) {
            className += 'large';
            iconSize = new L.Point(70, 70);
          } else if (childCount < 100 && childCount >= 10) {
            className += 'medium';
            iconSize = new L.Point(50, 50);
          } else {
            className += 'small';
            iconSize = new L.Point(30, 30);
          }

          return new L.DivIcon({
            html: '<div><span>' + childCount + '</span></div>',
            className: 'marker-cluster' + className,
            iconSize: iconSize
          });
        },
      });
      userCheckInData.forEach(function (checkin, index) {

        user_total_checkins += checkin.total_user_workout;
        total_workout_week += checkin.total_workout_week;
        total_active_studios += checkin.is_active;

        if (checkin.is_active) {

          for (var i = 0; i < checkin.total_user_workout; i++) {
            // Manually add markers to map
            var memberMarker = L.marker([checkin.latitude, checkin.longitude], {
              icon: new L.DivIcon({
                html: '<div><span>' + 1 + '</span></div>',
                className: 'marker-cluster marker-cluster-small',
                iconSize: [30, 30]
              })
            });
            currentMarkers.push(memberMarker);
            markerCluster.addLayer(memberMarker);
          }
        } else {
          var studioMarker = L.marker([checkin.latitude, checkin.longitude], {
            icon: L.icon({
              iconUrl: 'assets/images/f45-marker.png',
              iconSize: [33, 'auto'],
            })
          });
          currentMarkers.push(studioMarker);
          studioMarker.addTo(map);
        }
      });
      map.addLayer(markerCluster);
      if (isFirstExecution) {
        addDayNightOverlay(map);
      }

      // document.getElementById("studioTotal").innerHTML = formatNumber(total_active_studios);
      document.getElementById("userTotal").innerHTML = formatNumber(user_total_checkins);
      document.getElementById("globalVisitsTotal").innerHTML = formatNumber(total_workout_week);

    });
  } catch (error) {
    console.log(error);
  }
}

function httpGetAsync(theUrl, callback) {
  var xmlHttp = new XMLHttpRequest();
  xmlHttp.onreadystatechange = function () {
    if (xmlHttp.readyState == 4 && xmlHttp.status == 200)
      callback(xmlHttp.responseText);
  }
  xmlHttp.open("GET", theUrl, true);
  xmlHttp.send(null);
}

function addDayNightOverlay(map) {
  var terminator = L.terminator({
    fillColor: '#000',
    fillOpacity: 0.3,
    resolution: 2
  });
  terminator.addTo(map);
  setInterval(function () {
    updateTerminator(terminator);
  }, 60 * 1000);

  function updateTerminator(terminator) {
    terminator.setTime();
  }
}
