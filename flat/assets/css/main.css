:root {
  --body-font-family: "gotham", Helvetica, Arial, sans-serif;
  --heading-font-family: "gotham book", Helvetica, Arial, sans-serif;
  --primary-color: #3B29AD;
  --secondary-color: #d6001c;
  --secondary-color-darken: #F11212;
}

@-webkit-keyframes changeColorSecondary {
  0% {
    background-color: var(--secondary-color);
  }
  70% {
    background-color: var(--secondary-color);
  }
  100% {
    background-color: var(--secondary-color-darken);
  }
}

@keyframes changeColorSecondary {
  0% {
    background-color: var(--secondary-color);
  }
  70% {
    background-color: var(--secondary-color);
  }
  100% {
    background-color: var(--secondary-color-darken);
  }
}
@font-face {
  font-family: gotham;
  src: url(/assets/fonts/Gotham-Bold.eot);
  src: url(/assets/fonts/Gotham-Bold.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Bold.woff2) format("woff2"), url(assets/fonts/Gotham-Bold.woff) format("woff"), url(assets/fonts/Gotham-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: gotham;
  src: url(/assets/fonts/gotham-medium.eot);
  src: url(/assets/fonts/gotham-medium.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/gotham-medium.woff) format("woff"), url(assets/fonts/gotham-medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: "gotham book";
  src: url(/assets/fonts/Gotham-Book.eot);
  src: url(/assets/fonts/Gotham-Book.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Book.woff2) format("woff2"), url(assets/fonts/Gotham-Book.woff) format("woff"), url(assets/fonts/Gotham-Book.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
}
body {
  margin: 0;
  padding: 0;
  font-family: var(--body-font-family);
  font-size: 14px;
  font-weight: 500;
}

#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}

.mapboxgl-ctrl-logo {
  display: none !important;
}

.mapboxgl-ctrl-bottom-right {
  display: none !important;
}

.total-navbar {
  position: absolute;
  top: 0px;
  left: 0;
  width: 100%;
  color: white;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.navbar-layout {
  background-color: var(--primary-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.navbar-layout > * {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
}

.logo {
  -o-object-fit: cover;
  object-fit: cover;
  min-width: 96px;
}

.total-col-info {
  padding: 5px 10px;
  border-left: 2px solid rgba(255, 255, 255, 0.5);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
@media screen and (min-width: 768px) {
  .total-col-info {
    padding: 15px 20px;
  }
}
@media screen and (min-width: 1025px) {
  .total-col-info {
    padding: 20px 30px;
  }
}
.total-col-info .label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  color: rgba(255, 255, 255, 0.72);
  margin-bottom: 8px;
  white-space: nowrap;
  font-size: 13px;
}
.total-col-info .status {
  border-radius: 11px;
  background-color: rgba(255, 255, 255, 0.72);
  display: inline-block;
  text-transform: uppercase;
  font-size: 9px;
  padding: 3px 6px;
  color: var(--primary-color);
  white-space: nowrap;
  position: relative;
  margin-left: 8px;
}
.total-col-info .status:before {
  position: absolute;
  top: 50%;
  left: 6px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  display: inline-block;
  content: "";
  border-radius: 100%;
  display: none;
}
.total-col-info .status.active {
  padding-left: 18px;
}
.total-col-info .status.active:before {
  display: block;
  background-color: var(--secondary-color);
  -webkit-animation: changeColorSecondary 1s infinite alternate;
  animation: changeColorSecondary 1s infinite alternate;
}
.total-col-info .total {
  font-size: 26px;
  position: relative;
  left: -2px;
  white-space: nowrap;
}
.total-col-info .total small {
  font-weight: 400;
  font-size: 20px;
}

.mapbox-logo {
  display: none !important;
}

/* marker cluster */
#map .marker-cluster div {
  display: flex;
  align-items: center;
  justify-content: center;
}

#map .marker-cluster {
  background-color: rgba(59, 41, 173, 0.4);
}

#map .marker-cluster div {
  background-color: rgba(59, 41, 173, 0.9);
}

#map .marker-cluster span {
  color: #fff;
}

#map .marker-cluster-large {
  border-radius: 30px;
}

#map .marker-cluster-large div {
  margin-left: 8px;
  margin-top: 8px;
  width: 44px;
  height: 44px;
  border-radius: 22px;
}

#map .marker-cluster-small {
  border-radius: 15px;
}

#map .marker-cluster-small div {
  margin-left: 5px;
  margin-top: 5px;
  width: 20px;
  height: 20px;
  border-radius: 15px;
}
/* end marker cluster */

.legend {
  position: absolute;
  left: 50px;
  bottom: 50px;
  background-color: red;
  width: 100px;
  height: 100px;
  background-color: rgba(59, 41, 173, 0.4);
  border-radius: 50%;
}

.legend .legend__inner {
  margin-left: 10%;
  margin-top: 10%;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background-color: rgba(59, 41, 173, 0.9);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
}

#map .marker-cluster {
  background-color: rgba(59, 41, 173, 0.4);
  border-radius: 50%;
  z-index: 10000 !important
}

#map .marker-cluster-very-large span {
  font-size: 16px
}

#map .marker-cluster-large span {
  font-size: 15px
}

#map .marker-cluster-medium span {
  font-size: 14px
}

#map .marker-cluster-small span {
  font-size: 13px;
  padding-left: 1px
}

#map .marker-cluster div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: rgba(59, 41, 173, 0.9);
  border-radius: 50%;
  margin-left: 15%;
  margin-top: 15%;
  width: 70%;
  height: 70%;
  font-family: var(--body-font-family)
}

#map .marker-cluster span {
  color: #fff;
  line-height: 1;
  padding-top: 2px
}