@font-face {
    font-family: gotham;
    src: url(/assets/fonts/Gotham-Bold.eot);
    src: url(/assets/fonts/Gotham-Bold.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Bold.woff2) format("woff2"), url(assets/fonts/Gotham-Bold.woff) format("woff"), url(assets/fonts/Gotham-Bold.ttf) format("truetype");
    font-weight: 700;
    font-style: normal
}

@font-face {
    font-family: gotham;
    src: url(/assets/fonts/gotham-medium.eot);
    src: url(/assets/fonts/gotham-medium.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/gotham-medium.woff) format("woff"), url(assets/fonts/gotham-medium.ttf) format("truetype");
    font-weight: 500;
    font-style: normal
}

@font-face {
    font-family: "gotham book";
    src: url(/assets/fonts/Gotham-Book.eot);
    src: url(/assets/fonts/Gotham-Book.eot?#iefix) format("embedded-opentype"), url(/assets/fonts/Gotham-Book.woff2) format("woff2"), url(assets/fonts/Gotham-Book.woff) format("woff"), url(assets/fonts/Gotham-Book.ttf) format("truetype");
    font-weight: 400;
    font-style: normal
}

body {
    margin: 0;
    padding: 0;
    font-family: "gotham", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 500
}

#map {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%
}

.mapboxgl-ctrl-logo {
    display: none !important
}

.mapboxgl-ctrl-bottom-right {
    display: none !important
}

.total-navbar {
    position: absolute;
    top: 0px;
    left: 0;
    width: 100%;
    color: white;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.navbar-layout {
    background-color: #303947;
    position: relative;
}

.total-col-layout {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    padding-left: 112.5px;
}

/*.navbar-layout > * {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%
}*/

/*.logo-wrap {*/
/*    height: 0;*/
/*    padding-bottom: 112.5px;*/
/*    width: 112.5px;*/
/*    overflow: hidden;*/
/*    position: relative;*/
/*    flex-basis: 112.5px;*/
/*}*/

.logo {
    position: absolute;
    top: 0;
    left: 0;
    width: 112.5px;
    height: 100%;
    background-color: #231254;
}

.total-col-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center
}

.total-col-info + .total-col-info {
    border-left: 2px solid rgba(255, 255, 255, 0.2)
}

.total-col-info .heading-col {
    padding: 10px;
    background-color: #1C2430;
    font-family: "gotham book", Helvetica, Arial, sans-serif;
    letter-spacing: 1px;
    font-size: 12px;
    opacity: 0.8
}

.total-col-info .list-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.total-col-info .item-info {
    padding: 15px 40px;
    text-align: center
}

.total-col-info .label {
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
    font-size: 9px;
    margin-top: 5px;
    display: inline-block
}

.total-col-info .total {
    font-size: 32px;
    line-height: 1;
    position: relative;
    left: -2px;
    white-space: nowrap
}

.total-col-info .total small {
    font-weight: 400;
    font-size: 20px
}

.mapbox-logo {
    display: none !important
}

#map .marker-cluster {
    background-color: rgba(59, 41, 173, 0.4);
    border-radius: 50%;
    z-index: 10000 !important
}

#map .marker-cluster-very-large span {
    font-size: 16px
}

#map .marker-cluster-large span {
    font-size: 15px
}

#map .marker-cluster-medium span {
    font-size: 14px
}

#map .marker-cluster-small span {
    font-size: 13px;
    padding-left: 1px
}

#map .marker-cluster div {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: rgba(59, 41, 173, 0.9);
    border-radius: 50%;
    margin-left: 15%;
    margin-top: 15%;
    width: 70%;
    height: 70%;
    font-family: "gotham", Helvetica, Arial, sans-serif;
}

#map .marker-cluster span {
    color: #fff;
    line-height: 1;
    padding-top: 2px
}

.legend {
    position: absolute;
    left: 50px;
    bottom: 50px;
    background-color: red;
    width: 100px;
    height: 100px;
    background-color: rgba(59, 41, 173, 0.4);
    border-radius: 50%
}

.legend__inner {
    margin-left: 10%;
    margin-top: 10%;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background-color: rgba(59, 41, 173, 0.9);
    color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 13px
}