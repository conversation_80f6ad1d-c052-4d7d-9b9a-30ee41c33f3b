import { defineConfig } from 'vite'
import preact from '@preact/preset-vite'
import legacy from '@vitejs/plugin-legacy'
import { resolve } from 'path'
import * as path from "node:path";

export default defineConfig({
  plugins: [
    preact(),
    legacy({
      targets: ['Android 4.0', 'IE 11', 'Chrome 30'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    })
  ],
  build: {
    target: 'es2015',
    rollupOptions: {
      input: {
        consistency: resolve(__dirname, 'src/views/lionheart/consistency.html'),
        // discipline: resolve(__dirname, 'src/views/lionheart/discipline.html')
      },
      output: {
        entryFileNames: 'assets/[name].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash][extname]'
      }
    },
    outDir: 'dist',
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
})
