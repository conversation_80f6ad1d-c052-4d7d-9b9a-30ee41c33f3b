import { useEffect } from "preact/hooks";

export function useFullScreen() {
  useEffect(() => {
    const timer = setTimeout(() => {
      const height = window.innerHeight;

      const headingEl = document.querySelector<HTMLElement>(".t-heading");
      const bottomEl = document.querySelector<HTMLElement>(".table-bottom");
      const table = document.querySelector<HTMLElement>("#leaderboard");

      const heading = headingEl ? Math.ceil(headingEl.offsetHeight) : 0;
      const tableBottom = bottomEl ? Math.ceil(bottomEl.offsetHeight) : 0;

      const tableContent = Math.ceil(height - (heading + tableBottom));

      const rowCount = table ? table.querySelectorAll("tr").length + 1 : 1;

      // apply height to all table td
      const tds = document.querySelectorAll<HTMLElement>(".table td");
      const cellHeight = Math.ceil(tableContent / rowCount) - 3.2;

      tds.forEach((td) => {
        td.style.height = `${cellHeight}px`;
      });
    }, 100);

    return () => clearTimeout(timer);
  }, []);
}
