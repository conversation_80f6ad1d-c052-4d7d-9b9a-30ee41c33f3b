import {render} from 'preact'
import {useEffect, useMemo, useRef, useState} from 'preact/hooks'
import moment from 'moment'
import {Utility} from "../utils/helpers.js";
import {useFullScreen} from "@/hooks/useFullScreen.js";

type LeaderboardUserDay = { date: string; point?: number; is_top?: 0 | 1 }
type LeaderboardUser = {
  rank: number | string
  name: string
  avatar?: string
  total?: number
  data: LeaderboardUserDay[]
  challenge_flair?: boolean
  lionheart_flair?: boolean
  display_name?: string
}
type LeaderboardResponse = {
  studio?: { name: string }
  users?: LeaderboardUser[]
}

function findFrom<T>(
  array: T[] | undefined,
  predicate: (v: T, i: number, a: T[]) => boolean,
  thisArg?: any
): T | undefined {
  if(!array) return undefined
  for (let i = 0; i < array.length; i++) {
    if(predicate.call(thisArg, array[i], i, array)) return array[i]
  }
  return undefined
}

function next7Days(fromDate: string) {
  const out: string[] = []
  const start = moment(fromDate, 'YYYY-MM-DD')
  for (let i = 0; i < 7; i++) out.push(start.clone().add(i, 'days').format('YYYY-MM-DD'))
  return out
}

function useQueryParam(name: string) {
  return useMemo(() => Utility.getParamFromUrl(name), [name])
}

export default function Consistency() {
  // useFullScreen();
  const [isShowTable, setIsShowTable] = useState(false);
  const [today, setToday] = useState<string>(Utility.getCurrentDate())
  const [currentWeek, setCurrentWeek] = useState<{ startOfWeek: string; endOfWeek: string }>(
    Utility.getCurrentWeek(Utility.getParamFromUrl('from'))
  )
  const [daysOfWeek, setDaysOfWeek] = useState<string[]>(
    Utility.getDaysOfWeek(currentWeek.startOfWeek)
  )
  const [workouts, setWorkouts] = useState<Record<string, string> | null>(null)
  const [workoutsExpiredAt, setWorkoutsExpiredAt] = useState<moment.Moment | null>(null)
  const [leaderboard, setLeaderboard] = useState<LeaderboardResponse | null>(null)
  const [studio, setStudio] = useState<any>(null)

  const studio_code = useQueryParam('studio_code')
  const access_code = useMemo(() => {
    return Utility.getParamFromUrl('access_code') || Utility.getParamFromUrl('access_token') || ''
  }, [])

  const refreshTimer = useRef<number | null>(null)

  const isToday = (d: string) => today === d
  const workoutsExpired = () => (workoutsExpiredAt ? workoutsExpiredAt.isBefore(moment()) : false)

  useEffect(() => {
    if(!studio_code) {
      setIsShowTable(false);
      return
    }

    let stopped = false

    Utility.callAPI(
      `https://studio.api.f45training.com/v1/studios?page=0&search=CODE&studio_code[]=${studio_code}`,
      null,
      function (error: any, response: any) {
        if(stopped) return
        if(error) {
          console.log('No data')
          setIsShowTable(false);
          return
        }
        const s = response?.data?.studios?.[0]
        setStudio(s)

        setToday(Utility.getCurrentDate())
        fetchWorkouts()
        fetchLeaderboard()

        refreshTimer.current = window.setInterval(() => {
          setToday(Utility.getCurrentDate())
          fetchWorkouts()
          fetchLeaderboard()
        }, 1000 * 60 * 5)
      }
    )

    return () => {
      stopped = true
      if(refreshTimer.current) {
        clearInterval(refreshTimer.current)
        refreshTimer.current = null
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [studio_code])

  useEffect(() => {
    const cw = Utility.getCurrentWeek(Utility.getParamFromUrl('from'))
    setCurrentWeek(cw)
    setDaysOfWeek(Utility.getDaysOfWeek(cw.startOfWeek))
  }, [])

  function fetchLeaderboard() {
    if(!access_code || !studio_code) {
      console.log('No data')
      setIsShowTable(false);
      return
    }
    const url = `https://api.lionheart.f45.com/v2/studios/${studio_code}/leaderboards/weekly`
    const params = {
      dateFrom: currentWeek.startOfWeek,
      dateTo: moment(currentWeek.endOfWeek).add(1, 'days').format('YYYY-MM-DD'),
    }

    Utility.callAPI(
      url,
      params,
      function (error: any, response: { data: LeaderboardResponse }) {
        if(error) {
          console.log('No data')
          setIsShowTable(false);
        } else {
          setLeaderboard(response.data)
        }
      },
      null,
      true
    )
  }

  function fetchWorkouts() {
    if(!studio_code) return
    const params = {
      from: currentWeek.startOfWeek,
      to: currentWeek.endOfWeek,
      studio_code,
    }

    if(!workouts || workoutsExpired()) {
      Utility.callAPI( // vm.api.workouts
        'https://studio.api.f45training.com/v1/studio/leaderboard/workout_images',
        params,
        function (error: any, response: any) {
          if(error) {
            console.log('No data')
            setIsShowTable(false);
          } else {
            if(response?.data?.workouts) {
              const processed = Utility.processWorkoutData(response.data.workouts) as Record<
                string,
                string
              >
              setWorkouts(processed)
              setWorkoutsExpiredAt(moment().add(6, 'hours'))
            }
          }
        },
        null
      )
    }
  }

  // derived values
  const weekDates = daysOfWeek
  const startOfWeekStr = Utility.getDateByFormat(currentWeek.startOfWeek)
  const endOfWeekStr = Utility.getDateByFormat(currentWeek.endOfWeek)
  const users = leaderboard?.users || []

  useEffect(() => {
    // side effects to mimic your Utility calls after render:
    if(!leaderboard || !leaderboard.studio || !leaderboard.users || leaderboard.users.length <= 0) {
      setIsShowTable(false);
    } else {
      setIsShowTable(true);
      Utility.setFullScreen()
      // Utility.updateLeaderboardAssets()
    }
  }, [leaderboard])

  return (
    <>
    {isShowTable ? <table className="leaderboard">
      <thead id="top-heading">
      <tr className="t-heading">
        <th colSpan={3} className="text-start">
          <div className="logo d-inline-block ps-5">
            <div className="title">LIONHEART</div>
            <div className="d-flex sub-title align-items-center">
              <div className="flex-grow-1">
                <span className="red"></span>
                <span className="white"></span>
              </div>
              <div className="date">
                {startOfWeekStr} - {endOfWeekStr}
              </div>
            </div>
          </div>
        </th>

        {weekDates.map((d) => (
          <th key={d} className={isToday(d) ? 'today' : undefined}>
            {workouts ? <img width={88} src={workouts[d]}/> : null}
          </th>
        ))}

        <th>
          <img className="leaderboardLogo" width="82" src=""/>
        </th>
      </tr>

      <tr className="heading">
        <td width="100">RANK</td>
        <td width="90"></td>
        <td className="text-start">NAME</td>
        {weekDates.map((d) => {
          const dow = moment(d, 'YYYY-MM-DD').format('ddd')
          return (
            <td key={d} className={isToday(d) ? 'today' : undefined}>
              {dow}
            </td>
          )
        })}
        <td>TOTAL</td>
      </tr>

      {studio?.name ? (
        <tr>
          <td colSpan={3} className="text-start" id="studio-name">
            {(studio.name as string).replace('F45', 'F45 Training')}
          </td>
          <td colSpan={weekDates.length + 1}></td>
        </tr>
      ) : null}
      </thead>

      <tbody id="leaderboard">
      {users.length === 0 ? (
        <tr>
          <td colSpan={weekDates.length + 4}>No data</td>
        </tr>
      ) : (
        <UserRows
          users={users}
          weekStart={currentWeek.startOfWeek}
          todayStr={today}
          daysOfWeek={weekDates}
        />
      )}
      </tbody>
    </table> : <section id="lionheart_ads" className="no-data"
                        style="background-image: url('/assets/images/lionheart-leaderboard-bg.png')"></section>}

    </>
  )
}

function UserRows({
                    users,
                    weekStart,
                    todayStr,
                    daysOfWeek,
                  }: {
  users: LeaderboardUser[]
  weekStart: string
  todayStr: string
  daysOfWeek: string[]
}) {
  let nextRank: number | null = null
  let rankCount = 0

  const today = moment(todayStr, 'YYYY-MM-DD')
  const dates = next7Days(weekStart)

  return (
    <>
      {users.map((user, idx) => {
        const rankNum = parseInt(String(user.rank), 10)
        let rankDisplay: number | '' = ''
        if(nextRank !== rankNum) {
          rankDisplay = rankNum
          nextRank = rankNum
          rankCount = 0
        } else {
          rankCount++
        }

        const rankClass =
          rankDisplay === 1
            ? 'td-1'
            : rankDisplay === 2
              ? 'td-2'
              : rankDisplay === 3
                ? 'td-3'
                : undefined

        const displayName = Utility.getDisplayName({...user, display_name: user.name})

        return (
          <tr key={idx}>
            {/* rank */}
            <td>
              <div className="td">
                {rankClass ? <div className={rankClass}>{rankDisplay}</div> : <>{rankDisplay}</>}
              </div>
            </td>

            {/* avatar */}
            <td>
              <div className="td">
                <div className="avatar mx-auto">
                  {user.avatar ? (
                    <img src={Utility.convertToCdnUrl(user.avatar)}/>
                  ) : (
                    <>{Utility.getAvatarByName(user as any)}</>
                  )}
                </div>
              </div>
            </td>

            {/* name + flairs */}
            <td className="text-start user-name">
              <div className="td">
                {displayName}
                {user.challenge_flair ? (
                  <img className="challengeLogo" width="31" height="31" src="" alt=""/>
                ) : null}
                {user.lionheart_flair ? (
                  <img className="lionheartLogo" width="31" height="31" src="" alt=""/>
                ) : null}
              </div>
            </td>

            {/* 7 days */}
            {dates.map((d) => {
              const current = findFrom(user.data, (it) => it.date === d)
              const isToday = todayStr === d
              const tdClass = isToday ? 'today' : undefined

              if(!current) {
                // future blank vs past '-'
                if(today.isBefore(d)) {
                  return (
                    <td key={d} className={tdClass}>
                      <div className="td"></div>
                    </td>
                  )
                }
                return (
                  <td key={d} className={tdClass}>
                    <div className="td">-</div>
                  </td>
                )
              }

              const star = current.is_top === 1 ? '*' : ''
              const val = Utility.getNumber(current.point)
              const showVal = today.isAfter(current.date) ? val : today.isSame(current.date) ? (val !== '-' ? val : '') : ''

              return (
                <td key={d} className={tdClass}>
                  <div className="td">
                    {showVal}
                    {star}
                  </div>
                </td>
              )
            })}

            {/* total */}
            <td>
              <div className="td">{Utility.getNumber(user.total)}</div>
            </td>
          </tr>
        )
      })}

      {/* pad studio rows like original */}
      {Utility.addStudioRows(10 - users.length, daysOfWeek, todayStr)}
    </>
  )
}


render(<Consistency/>, document.getElementById('consistency')!)

