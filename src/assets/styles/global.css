@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Bold.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Book.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Book.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Medium.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('../fonts/GothamCondensed-Light.woff2') format('woff2'),
      url('../fonts/GothamCondensed-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Bold.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Black.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow Book';
  src: url('../fonts/GothamNarrow-Book.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Book.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Thin.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Light.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('../fonts/GothamNarrow-Medium.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow Ultra';
  src: url('../fonts/GothamNarrow-Ultra.woff2') format('woff2'),
      url('../fonts/GothamNarrow-Ultra.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

html {
  /* font-size: 67%; */
  background-color: #16142C;
  background: linear-gradient(90deg, #16142C 0%, #1F1D3E 34.9%, #1E1C3B 62.5%, #16142C 100%);

}
html.row-11 {
  /* font-size: 63% */
}
img {
  max-width: 100%;
}
@media (min-width: 1200px) and (max-width: 1400px) {
  /* html {
    font-size: 47.5%
  } */
}
@media(min-width: 2000px) {
  /* html {
    font-size: 89.5%
  }
  html.row-11 {
    font-size: 84%;
  } */
}

@media(min-width: 3000px) {
  /* html {
    font-size: 134%
  }
  html.row-11 {
    font-size:124%
  } */
}

th  img {
  width: 50px;
}

body {
  background-color: #16142C;
  background: linear-gradient(90deg, #16142C 0%, #1F1D3E 34.9%, #1E1C3B 62.5%, #16142C 100%);
  color: white;
  font-family: 'Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 20px;
  line-height: 120%;
  font-weight: 500;
}

#leaderboard_table {
  display: none;
}

.container-fluid  {
  padding: 0 10px 10px;
}

/* Fix for 1280 x 720 */
@media only screen and (max-width: 1300px) {

  .table-bottom .right {
    font-size: 14px;
  }
}


.td.text-center.ps-0 {
  font-style: normal;
}
.table {
  color: inherit;
  text-align: center;
  /* max-width: 99.99%; */
}
.table tr td{
  /* background: linear-gradient(90deg, rgba(156, 156, 164, 0.2) 0%, rgba(163, 163, 163, 0.05) 100%); */
  border-top: 2px solid #1F2937;
  border-bottom-width: 0;
}
.table tr:last-child td{
  border-bottom: 2px solid #1F2937!important;
}

#leaderboard_consistency.table thead tr:nth-child(2) td{
  border-top: none;
  opacity: 0.6;
}

#leaderboard_consistency.table tbody tr:first-child td{
  border-top: none;
}

#leaderboard_consistency.table tr td{
  border-top: 2px solid #273EA4;
  border-bottom-width: 0;
}
#leaderboard_consistency.table tr:last-child td{
  border-bottom: 2px solid #273EA4!important;
}
#leaderboard_consistency.table tr td:first-child{
  /* background: linear-gradient(90deg, rgba(156, 156, 164, 0.2) 0%, rgba(163, 163, 163, 0.05) 100%); */
  border: none !important;
}

#leaderboard_consistency.table tr td:nth-child(2){
  /* background: linear-gradient(90deg, rgba(156, 156, 164, 0.2) 0%, rgba(163, 163, 163, 0.05) 100%); */
  border: none !important;
}



.table tr.active {
  background: linear-gradient(90deg, #5451F7 0%, rgba(84, 81, 246, 0.05) 100%);
}
.table tr.first {
  background: linear-gradient(90deg, #C9BE5A 0%, rgba(201, 190, 90, 0.05) 100%);
}
.table tr.first td {
  background-color: transparent;
}
.table tr.secondary {
  background: linear-gradient(90deg, #9E9EA9 0%, rgba(158, 158, 169, 0.05) 100%);
}
.table tr.third {
  background: linear-gradient(90deg, #A26A3B 0%, rgba(161, 106, 59, 0.05) 100%);
}
.table tr td {
  font-style: italic;
  vertical-align: middle;
  box-shadow: none;
  color: #E5E7EB;
  font-weight: 400;
  padding: 0 0;
  height: 49px;
  }
  .row-11 .table tr td {
    height: 45px
  }
  .table tr.hightlight td {
    font-size: 24px;
  }
.table tr th {
  vertical-align: middle;
  /* font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; */
  font-size: 25px;
  padding-top: 14px;
  padding-bottom: 14px;
  border: none;
  text-transform: uppercase;
  text-align: center;
  background-color: transparent;
}
.table tr.t-heading {
  background: transparent;
  /* background: linear-gradient(90deg, #16142C 0%, #1B1A35 51.98%, #16142C 100%); */
}
tr.heading td:nth-child(1) {
  width: 90px;
}
tr.heading td:nth-child(2) {
  width: 90px;
}
tr.heading td:nth-child(3) {
  width: 320px;
}
.table tr td:first-child {
   font-style: normal;
   min-width: 90px;
}
.col-avatar {
  width: 34px;
}
.table tr td:last-child,.table tr th:last-child {
  border-radius:  0 10px 10px  0;
}
.table .average-row td {

  border-bottom: none;
}
.avatar {
  width: 32px;
  min-width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  font-size: 87%;
  background: #737B89;
  font-style: normal;
  font-weight: 400;
  border-radius: 50%;
}
.avatar-no-image {
  background: #C5C6CB;
}
.table tr td.user-name {
  font-style: normal;
}
.table tr td.user-name img {
  margin-left: 10px;
}
.logo.ps-5 {
  padding: 0 10px!important;
}
.table-bottom.px-5 {
  padding: 0 10px!important
}
.avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}


.title {
  font-size: 41px;
  line-height: 120%;
}
.t-heading th {
  text-align: left;
}
.t-heading .date {
  font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 17px;
}

.sub-title span {
  display: block;
  height: 3px;
  margin-right: 6px;
  background-color: white;
}

.sub-title .red {
  background: #D6001C;
  height: 5px;
  margin-bottom: 2px;
}
.table tr.heading {
  background: transparent;
  border: none;
}
.table tr.heading td {
  background: transparent;
  border: none;
  font-style: normal;
  font-weight: inherit;
  padding: 0 5px;
  font-size: 25px;
  text-transform: uppercase;
  font-family: 'Gotham Condensed','Gotham Narrow', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}
.today {
  background: rgba(255, 255, 255, 0.1)!important;
  border-bottom-color: #323149!important
}

.table tr:last-child td {
  border-bottom: none;
}
.table-bottom {
  gap: 14px;
}
.table-bottom .text {
  font-size: 14px;
}
.table-bottom .left {
  max-width: 33.333%;
  flex: 0 0 33.333%;
}
.table-bottom {
  line-height: 1.2;
  font-size: 16px;
  /* margin-top: 14px; */
}
.ps-5 {
  padding-left: 30px!important;
}
.ps-5 {
  padding-right: 30px!important;
}
.px-5 {
  padding-left: 30px!important;
  padding-right: 30px!important;
}
.table-bottom .left img {
width: 44px;
height: auto;
}
.table-bottom  .middle {
  color: #ABB0B7;
  font-size: 14px;
  text-align: center;
  font-weight: 300;
  max-width: 40.333%;
  flex: 0 0 40.333%;
}
.table-bottom .right {
  text-align: right;
  text-transform: uppercase;
  flex: 1
}
.col-flag .avatar{
  background-color: transparent;

}

.table tr.empty td ,.table tr.empty {
  background: none;
  padding-left: 0;
  border: none;

}

.table.region tr td .flag img {
  min-width:34px;
  width:34px;
  background: none;
  height: 25px;
}
.table tr.empty td{
  border-top: none;
  border-bottom: .48rem solid transparent;
  vertical-align: middle;
  line-height: 1;
}
.table tr.empty + tr td{
  border-top:none;
}


.table.region tr.top-1 td {
  font-size: 24px;
}
.table.region tr.top-1 td:first-child {
  font-size: 20px;
}
.table.region tr.top-1 td.user-name {
  font-size:24px;
}

.current .td {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #5451F7;
  padding: 0;
}
.current .user-name .td {
  justify-content: flex-start;
}
.current td,.current + tr td {
  border-color: transparent!important
}
.current td:first-child .td {
   border-radius: 10px 0 0 10px;
}
.current td:last-child .td {
   border-radius:  0 10px 10px 0;
}

.top-1 .td,.top-2 .td,.top-3 .td {
  padding-left: 0;
}

.td-2 ,.td-1,.td-3 {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 3px solid #C9BE5A;
  margin: auto;
}
.td-2{
  border-color: #9E9EA9;
}
.td-3{
  border-color: #A16A3B;
}

/* full hd */
@media only screen and (min-width: 1800px) {
  body {
    font-size: 30px;
  }
  th img {
    width: 88px;
  }
  .table tr.heading td {
    font-size: 38px;
  }
  .table tr td {
    height: 73px;
  }
  .row-11 .table tr td {
    height: 67px;
  }
  .title {
    font-size: 62px;
  }
  .t-heading .date {
    font-size: 26px;
  }
  .sub-title .red {
    height: 7px;
  }
  .table-bottom .text {
    font-size: 22px;
  }
  .avatar ,.avatar  img{
    width: 52px;
    height: 52px;
  }
  .td-2, .td-1, .td-3 {
    width: 55px;
    height: 55px;
    border-width: 4px;
  }
  .table-bottom .left img {
    width: 66px;
  }
  .sub-title span {
    height: 4px;
  }
  .table-bottom .middle {
    font-size: 24px;
  }
  .table-bottom .right {
    font-size: 24px;
  }
  tr.heading td:nth-child(1) {
    width: 110px;
  }
  tr.heading td:nth-child(2) {
    width: 130px;
  }
  tr.heading td:nth-child(3) {
    width: 400px;
  }
}


/* 2k */
@media only screen and (min-width: 2000px) {
  body {
    font-size: 40px;
  }
  th img {
    width: 100px;
  }
  .table tr.heading td {
    font-size: 48px;
  }
  .table tr td {
    height: 105px;
  }
  .row-11 .table tr td {
    height: 96px;
  }
  .title {
    font-size: 72px;
  }
  .t-heading .date {
    font-size: 36px;
  }
  .sub-title .red {
    height: 8px;
  }
  .table-bottom .text {
    font-size: 33px;
  }
  .avatar ,.avatar  img{
    width: 62px;
    height: 62px;
  }
  .td-2, .td-1, .td-3 {
    width: 65px;
    height: 65px;
    border-width: 5px;
  }
  .table-bottom .left img {
    width: 86px;
  }
  .sub-title span {
    height: 5px;
  }
  .table-bottom .middle {
    font-size: 34px;
  }
  .table-bottom .right {
    font-size: 34px;
  }
  tr.heading td:nth-child(1) {
    width: 140px;
  }
  tr.heading td:nth-child(2) {
    width: 180px;
  }
  tr.heading td:nth-child(3) {
    width: 700px;
  }
}




/* 4k  */
@media only screen and (min-width: 3000px) {
  body {
    font-size: 70px;
  }
  th img {
    width: 200px;
  }
  .table tr.heading td {
    font-size: 88px;
  }
  .table tr td {
    height: 152px;
  }
  .row-11 .table tr td {
    height: 140px;
  }
  .title {
    font-size: 142px;
  }
  .t-heading .date {
    font-size: 66px;
  }
  .sub-title .red {
    height: 16px;
  }

  .avatar ,.avatar  img{
    width: 100px;
    height: 100px;
  }
  .td-2, .td-1, .td-3 {
    width: 110px;
    height: 110px;
    border-width: 8px;
  }
  .table-bottom .left img {
    width: 146px;
  }
  .sub-title span {
    height: 10px;
  }
  .table-bottom .text {
    font-size: 46px;
  }
  .table-bottom .middle {
    font-size: 44px;
  }
  .table-bottom .right {
    font-size: 44px;
  }
  tr.heading td:nth-child(1) {
    width: 240px;
  }
  tr.heading td:nth-child(2) {
    width: 280px;
  }
  tr.heading td:nth-child(3) {
    width: 1000px;
  }
}


.no-data {
  background-position: center;
  background-size: cover;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  display: none;
}

.average {
  display: none;
}