<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Fri Sep 12 16:35:18 2008
 By <PERSON><PERSON><PERSON>,,,
Copyright (C) 2001, 2008 Hoefler &#38; <PERSON><PERSON>-<PERSON>. http://www.typography.com
</metadata>
<defs>
<font id="GothamCondensed-Bold" horiz-adv-x="240" >
  <font-face 
    font-family="Gotham Condensed Bold"
    font-weight="400"
    font-stretch="condensed"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="800"
    descent="-200"
    x-height="536"
    cap-height="700"
    bbox="-142 -230 804 937"
    underline-thickness="50"
    underline-position="-200"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="500" 
d="M0 -200v1000h500v-1000h-500zM71 750l179 -390l179 390h-358zM50 -76l172 376l-172 376v-752zM450 676l-172 -376l172 -376v752zM250 240l-179 -390h358z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="460" 
d="M276 0v440h-31v96h31v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118zM46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h59v-96h-59v-440h-118z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="420" 
d="M46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118zM268 0v536h118v-536h-118zM267 587v118h120v-118h-120z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="420" 
d="M46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118zM268 0v705h118v-705h-118z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="650" 
d="M276 0v440h-31v96h31v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118zM46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h59v-96h-59v-440h-118zM498 0v536h118v-536h-118z
M497 587v118h120v-118h-120z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="650" 
d="M276 0v440h-31v96h31v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118zM46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h59v-96h-59v-440h-118zM498 0v705h118v-705h-118z
" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M0 -200v1000h500v-1000h-500zM71 750l179 -390l179 390h-358zM50 -76l172 376l-172 376v-752zM450 676l-172 -376l172 -376v752zM250 240l-179 -390h358z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="159" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="222" 
d="M81 218l-42 462v20h144v-20l-42 -462h-60zM42 0v139h138v-139h-138z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="345" 
d="M210 381v319h120v-9l-76 -310h-44zM45 381v319h120v-9l-76 -310h-44z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="377" 
d="M346 268v-108h-65l-14 -160h-77l14 160h-64l-14 -160h-77l14 160h-50v108h59l15 168h-56v108h65l14 156h77l-14 -156h64l14 156h77l-14 -156h50v-108h-59l-15 -168h56zM164 436l-15 -168h64l15 168h-64z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="350" 
d="M303 683v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104q0 -65 -32.5 -111.5t-85.5 -63.5v-110h-79v103q-50 4 -100 28v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 64 29.5 108.5
t80.5 62.5v80h79v-70q44 -2 89 -21z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="570" 
d="M137 332q-51 0 -81.5 47.5t-30.5 128.5v22q0 81 30.5 128.5t81.5 47.5t81.5 -47.5t30.5 -128.5v-22q0 -81 -30.5 -128.5t-81.5 -47.5zM137 625q-30 0 -30 -96v-20q0 -96 30 -96t30 96v20q0 96 -30 96zM258 364l156 336h71l-171 -364l-156 -336h-71zM433 -6
q-51 0 -81.5 47.5t-30.5 128.5v22q0 81 30.5 128.5t81.5 47.5t81.5 -47.5t30.5 -128.5v-22q0 -81 -30.5 -128.5t-81.5 -47.5zM433 75q30 0 30 96v20q0 96 -30 96t-30 -96v-20q0 -96 30 -96z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="434" 
d="M345 -13l-34 62q-53 -59 -130 -59q-70 0 -119 53t-49 136v6q0 113 107 206q-59 95 -59 159v7q0 64 40.5 108.5t104.5 44.5q63 0 102 -42t39 -110v-7q0 -104 -99 -188l63 -120q4 19 15 94l94 -17q-12 -106 -45 -180l58 -99zM200 445q43 42 43 103v8q0 25 -11 40.5
t-28 15.5q-18 0 -28.5 -14.5t-10.5 -37.5v-10q0 -38 35 -105zM196 96q36 0 63 33l-88 174q-41 -50 -41 -109v-8q0 -41 18 -65.5t48 -24.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="180" 
d="M45 381v319h120v-9l-76 -310h-44z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="299" 
d="M272 -139q-119 35 -174.5 148.5t-55.5 278.5q0 163 55.5 277t174.5 149v-88q-117 -61 -117 -338q0 -278 117 -339v-88z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="299" 
d="M27 -139v88q117 61 117 339q0 277 -117 338v88q119 -35 174.5 -149t55.5 -277q0 -165 -55.5 -278.5t-174.5 -148.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="298" 
d="M109 394l18 112l-74 -66l-40 70l91 37l-91 37l40 70l74 -66l-18 112h80l-18 -112l74 66l40 -70l-91 -37l91 -37l-40 -70l-74 66l18 -112h-80z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="380" 
d="M135 173v122h-113v114h113v122h110v-122h113v-114h-113v-122h-110z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="218" 
d="M28 -162l-10 49q51 11 69.5 42.5t14.5 70.5h-62v139h138v-114q0 -80 -38 -128.5t-112 -58.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="336" 
d="M39 250v120h258v-120h-258z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="218" 
d="M40 0v139h138v-139h-138z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="308" 
d="M-9 -98l219 866h99l-219 -866h-99z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="394" 
d="M197 -10q-87 0 -130 81.5t-43 259.5v38q0 178 43 259.5t130 81.5q173 0 173 -341v-38q0 -341 -173 -341zM197 102q16 0 26.5 15t18 68t7.5 147v36q0 94 -7.5 147t-18 68t-26.5 15t-27 -15t-18.5 -68t-7.5 -147v-36q0 -94 7.5 -147t18.5 -68t27 -15z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="253" 
d="M87 0v571l-73 -23v123l108 34h87v-705h-122z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="332" 
d="M21 0v103l127 268q51 108 51 154q0 28 -17 47.5t-50 19.5q-44 0 -97 -34v118q66 34 125 34q72 0 115 -43.5t43 -120.5v-5q0 -82 -54 -191l-113 -230h164v-120h-294z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="328" 
d="M118 -10q-58 0 -104 19v116q46 -19 88 -19q38 0 62.5 26.5t24.5 73.5v8q0 100 -122 108v80l114 179h-158v119h282v-109l-118 -183q122 -41 122 -185v-11q0 -102 -53.5 -162t-137.5 -60z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="386" 
d="M204 0v129h-190v93l184 480h120v-468h57v-105h-57v-129h-114zM113 234h91v256z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="333" 
d="M122 -10q-49 0 -93 19v119q40 -21 80 -21q42 0 65.5 32.5t23.5 89.5v7q0 100 -67 100q-24 0 -48 -10l-59 39l19 335h249v-116h-159l-9 -142q22 5 47 5q63 0 105 -50.5t42 -159.5v-9q0 -112 -53.5 -175t-142.5 -63z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="368" 
d="M188 -10q-58 0 -95 37q-69 69 -69 290v26q0 105 14.5 178.5t42 114t61.5 57.5t79 17q43 0 84 -16v-115q-36 21 -71 21q-45 0 -69 -39.5t-25 -137.5q36 30 79 30q55 0 93 -48.5t38 -155.5v-21q0 -107 -45.5 -172.5t-116.5 -65.5zM189 99q47 0 47 124v22q0 107 -52 107
q-23 0 -43 -18v-86q0 -149 48 -149z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="331" 
d="M57 0l133 577h-175v123h292v-86l-131 -614h-119z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="372" 
d="M186 -10q-73 0 -119.5 55t-46.5 149v7q0 63 23.5 107t57.5 67q-71 46 -71 149v6q0 82 45 131t111 49t111 -49t45 -131v-6q0 -103 -71 -149q34 -23 57.5 -67t23.5 -107v-7q0 -94 -46.5 -149t-119.5 -55zM186 412q44 0 44 100v6q0 96 -44 96t-44 -96v-6q0 -100 44 -100z
M186 87q49 0 49 113v7q0 111 -49 111t-49 -111v-7q0 -113 49 -113z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="368" 
d="M134 100q43 0 67.5 38t26.5 130q-35 -29 -79 -29q-56 0 -93.5 49.5t-37.5 161.5v16q0 112 45 178t117 66q58 0 95 -37q69 -69 69 -290v-26q0 -105 -14.5 -178.5t-42 -114t-61.5 -57.5t-79 -17q-46 0 -94 19v115q46 -24 81 -24zM184 340q23 0 43 18v94q0 149 -48 149
q-47 0 -47 -130v-17q0 -114 52 -114z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="228" 
d="M45 397v139h138v-139h-138zM45 0v139h138v-139h-138z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="228" 
d="M45 397v139h138v-139h-138zM33 -162l-10 49q51 11 69.5 42.5t14.5 70.5h-62v139h138v-114q0 -80 -38 -128.5t-112 -58.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="380" 
d="M346 106l-317 192v108l317 192v-141l-193 -104l193 -104v-143z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="380" 
d="M24 388v120h332v-120h-332zM24 196v120h332v-120h-332z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="380" 
d="M34 106v141l192 104l-192 104v143l317 -192v-108z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="320" 
d="M97 218l-11 175l4 5q44 14 70 43.5t26 70.5v2q0 34 -23 56.5t-58 22.5q-46 0 -85 -24v110q54 26 109 26q79 0 128 -50.5t49 -128.5v-9q0 -126 -117 -179l-12 -120h-80zM70 0v139h138v-139h-138z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="636" 
d="M298 -137q-121 0 -196.5 91t-74.5 261q1 129 40 240.5t116.5 183.5t177.5 72q109 0 177.5 -88.5t68.5 -216.5q0 -87 -18.5 -162.5t-57.5 -125.5t-92 -50q-86 0 -99 81q-43 -77 -98 -77q-37 0 -61 31t-24 89q0 124 44 219.5t103 95.5q54 0 72 -64l10 56l96 -16l-44 -254
q-7 -39 -7 -67q0 -45 31 -45q40 0 70.5 89t30.5 205q0 109 -56 183.5t-151 74.5q-83 0 -149.5 -67t-101 -171.5t-34.5 -220.5q-1 -146 62.5 -226t169.5 -80q81 0 146 39l21 -35q-75 -45 -172 -45zM285 156q10 0 20 10q23 23 37 89.5t14 113.5q0 45 -28 45q-10 0 -20 -10
q-23 -23 -37 -89.5t-14 -113.5q0 -45 28 -45z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="392" 
d="M36 0v700h104q123 0 182 -59q46 -46 46 -119v-5q0 -110 -101 -158q110 -48 110 -156v-9q0 -91 -64 -142.5t-168 -51.5h-109zM248 498v2q0 87 -90 87h-2v-181h2q42 0 66 23.5t24 68.5zM255 209q0 43 -26.5 67t-69.5 24h-3v-187h3q43 0 69.5 24t26.5 67v5z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="336" 
d="M221 -5q-89 0 -143 84.5t-54 258.5v24q0 175 56.5 259t148.5 84q45 0 80 -16v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-37 -21 -90 -21z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="419" 
d="M36 0v700h114q118 0 181.5 -85.5t63.5 -252.5v-24q0 -167 -63.5 -252.5t-181.5 -85.5h-114zM163 588h-5v-476h5q108 0 108 226v24q0 226 -108 226z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="310" 
d="M36 0v700h255v-116h-133v-176h116v-116h-116v-292h-122z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="405" 
d="M247 -10q-99 0 -161 86t-62 262v24q0 168 63.5 258t170.5 90q51 0 93 -20v-127q-50 30 -89 30q-54 0 -84 -56.5t-30 -169.5v-34q0 -230 104 -230q5 0 11 1v184h-60v109h171v-377q-69 -30 -127 -30z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="422" 
d="M36 0v700h122v-290h106v290h122v-700h-122v294h-106v-294h-122z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="295" 
d="M93 -6q-33 0 -77 10v121q33 -12 59 -12q64 0 64 72v515h122v-524q0 -92 -47 -139q-43 -43 -121 -43z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="403" 
d="M36 0v700h122v-300l101 300h134l-117 -296l119 -404h-132l-77 268l-28 -78v-190h-122z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="300" 
d="M36 0v700h122v-584h129v-116h-251z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="486" 
d="M36 0v700h123l84 -266l84 266h123v-700h-117v425l-89 -267h-2l-89 267v-425h-117z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="428" 
d="M36 0v700h113l126 -362v362h117v-700h-107l-132 379v-379h-117z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="385" 
d="M36 0v700h109q104 0 165 -58.5t61 -177.5v-2q0 -114 -57 -174.5t-155 -67.5h-1v-220h-122zM158 333h1q41 2 65.5 33t24.5 92v2q0 127 -87 127h-4v-254z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="427" 
d="M421 49l-61 -65l-48 50q-43 -44 -99 -44q-89 0 -139 86.5t-50 255.5v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -138 -38 -225zM219 100q13 0 22 8l-60 59l57 63l29 -31q5 55 5 133v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -95 9 -147
t23 -68.5t39 -16.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="398" 
d="M36 0v700h126q102 0 158 -56q58 -58 58 -167v-9q0 -122 -82 -184l94 -284h-130l-79 244h-23v-244h-122zM158 354h6q92 0 92 113v8q0 111 -95 111h-3v-232z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="325" 
d="M145 -5q-64 0 -124 29v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 82 48.5 132t124.5 50q55 0 105 -22v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104q0 -81 -50 -132t-123 -51z
" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="324" 
d="M101 0v584h-86v116h294v-116h-86v-584h-122z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="378" 
d="M126 -2l-125 702h124l66 -456l66 456h120l-125 -702h-126z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="592" 
d="M109 -2l-106 702h125l51 -429l61 429h113l60 -429l52 429h122l-106 -702h-128l-58 415l-58 -415h-128z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="392" 
d="M253 700h127l-110 -343l114 -357h-130l-59 213l-60 -213h-127l114 357l-110 343h130l55 -203z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="382" 
d="M130 0v276l-130 424h126l66 -262l67 262h123l-130 -424v-276h-122z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="340" 
d="M23 0v97l161 488h-155v115h289v-97l-161 -488h161v-115h-295z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="286" 
d="M62 -130v830h196v-84h-93v-662h93v-84h-196z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="308" 
d="M218 -98l-219 866h99l219 -866h-99z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="286" 
d="M28 -130v84h93v662h-93v84h196v-830h-196z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="500" 
d="M128 502l73 191h97l74 -191h-76l-47 129l-47 -129h-74z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="452" 
d="M-2 -151v92h456v-92h-456z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="500" 
d="M252 575l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="384" 
d="M245 -10q-24 0 -44 14t-28.5 26t-21.5 35v-65h-118v705h118v-236q12 22 21 35.5t29 27.5t44 14q114 0 114 -254v-48q0 -254 -114 -254zM197 103q44 0 44 147v36q0 147 -44 147q-47 0 -47 -147v-36q0 -147 47 -147z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="286" 
d="M189 -10q-77 0 -121.5 67.5t-44.5 193.5v32q0 125 47 193t122 68q43 0 74 -14v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-40 -22 -78 -22z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v234h118v-705h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="230" 
d="M46 0v440h-33v96h33v39q0 65 33 98q34 34 102 34q28 0 44 -4v-98q-5 1 -14 1q-47 0 -47 -49v-21h61v-96h-61v-440h-118z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="384" 
d="M163 -132q-57 0 -116 17v109q61 -22 100 -22q86 0 86 84v53q-11 -21 -21 -35.5t-30 -28t-44 -13.5q-113 0 -113 254v6q0 254 113 254q24 0 44 -13t29.5 -26.5t21.5 -35.5v65h118v-489q0 -87 -46 -133t-142 -46zM187 145q47 0 47 143v2q0 70 -12.5 106.5t-34.5 36.5
q-44 0 -44 -143v-2q0 -143 44 -143z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="379" 
d="M33 0v705h118v-233q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="186" 
d="M33 587v118h120v-118h-120zM34 0v536h118v-536h-118z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="188" 
d="M28 -133q-26 0 -47 4v93h11q44 0 44 43v529h118v-539q0 -130 -126 -130zM35 587v118h120v-118h-120z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="357" 
d="M33 0v705h118v-379l77 210h126l-92 -217l88 -319h-129l-48 206l-22 -59v-147h-118z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="186" 
d="M34 0v705h118v-705h-118z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="576" 
d="M33 0v536h118v-63q39 73 95 73t90 -62q45 62 96 62q46 0 78.5 -37.5t32.5 -104.5v-404h-118v378q0 62 -38 62q-18 0 -29 -16.5t-11 -45.5v-378h-118v378q0 62 -38 62q-18 0 -29 -16.5t-11 -45.5v-378h-118z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="379" 
d="M33 0v536h118v-64q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="384" 
d="M33 -130v666h118v-67q12 22 21 35.5t29 27.5t44 14q114 0 114 -254v-48q0 -254 -114 -254q-24 0 -44 14t-28.5 26t-21.5 35v-195h-118zM197 103q44 0 44 147v36q0 147 -44 147q-47 0 -47 -147v-36q0 -147 47 -147z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="384" 
d="M233 -130v197q-12 -22 -21 -35.5t-29 -27.5t-44 -14q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-666h-118zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="250" 
d="M33 0v536h118v-67q27 77 86 73v-142h-2q-84 0 -84 -114v-286h-118z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="284" 
d="M126 -10q-52 0 -109 25v116q67 -35 101 -35q44 0 44 43q0 17 -11.5 35t-48.5 54q-46 45 -68.5 83.5t-22.5 85.5v2q0 66 41.5 105.5t110.5 39.5q46 0 88 -14v-116q-46 24 -81 24q-21 0 -33 -10t-12 -27q0 -16 12 -32.5t52 -56.5q49 -48 68 -85t19 -83v-2
q0 -69 -42.5 -110.5t-107.5 -41.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="244" 
d="M157 -7q-110 0 -110 99v348h-32v96h32v137h118v-137h63v-96h-63v-308q0 -33 33 -33q14 0 29 6v-94q-32 -18 -70 -18z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="362" 
d="M121 -2l-111 538h117l55 -348l55 348h115l-110 -538h-121z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="526" 
d="M103 -2l-92 538h119l41 -309l47 309h92l47 -309l41 309h117l-91 -538h-112l-49 301l-48 -301h-112z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="356" 
d="M132 536l47 -160l47 160h120l-95 -261l97 -275h-124l-47 170l-47 -170h-122l97 275l-95 261h122z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="362" 
d="M179 220l56 316h116l-148 -666h-115l34 149l-111 517h117z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="290" 
d="M12 0v84l141 352h-136v100h262v-84l-141 -352h141v-100h-267z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="311" 
d="M281 -141q-92 2 -139 46.5t-47 116.5q0 25 13.5 67t13.5 72q0 37 -21.5 57.5t-57.5 20.5h-19v96h19q35 0 57 20.5t22 57.5q0 30 -13.5 72t-13.5 67q0 72 47 116.5t139 46.5v-80q-33 -1 -58 -22t-25 -57q0 -31 15.5 -74.5t15.5 -70.5q0 -52 -33 -84t-86 -40
q54 -8 86.5 -39.5t32.5 -84.5q0 -27 -15.5 -70.5t-15.5 -74.5q0 -36 25 -57t58 -22v-80z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="249" 
d="M78 -98v866h93v-866h-93z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="311" 
d="M30 -141v80q33 1 58 22t25 57q0 31 -15.5 74.5t-15.5 70.5q0 52 33 84t86 40q-54 8 -86.5 39.5t-32.5 84.5q0 27 15.5 70.5t15.5 74.5q0 36 -25 57t-58 22v80q92 -2 139 -46.5t47 -116.5q0 -25 -13.5 -67t-13.5 -72q0 -37 21.5 -57.5t57.5 -20.5h19v-96h-19
q-35 0 -57 -20.5t-22 -57.5q0 -30 13.5 -72t13.5 -67q0 -72 -47 -116.5t-139 -46.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="369" 
d="M246 254q-32 0 -67 25q-30 20 -46 20q-25 0 -38 -49l-58 18q6 55 31 82.5t55 27.5q32 0 67 -25q30 -20 46 -20q25 0 38 49l58 -18q-6 -55 -31 -82.5t-55 -27.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="159" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="222" 
d="M42 561v139h138v-139h-138zM39 0v20l42 462h60l42 -462v-20h-144z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="300" 
d="M214 705v-77q26 -1 54 -13v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-31 -17 -55 -21v-82h-79v91q-110 47 -110 251v32q0 98 29.5 161.5t80.5 86.5v89h79z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="350" 
d="M22 0v81l49 24v175h-46v111h46v111q0 111 51 162q44 44 118 44q42 0 76 -9v-120q-28 10 -55 10q-30 0 -48 -18q-20 -20 -20 -68v-112h96v-111h-96v-169h142v-111h-313z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="402" 
d="M55 249v92h65l-110 359h122l70 -285l71 285h119l-110 -359h65v-92h-87v-60h87v-92h-87v-97h-118v97h-87v92h87v60h-87z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="409" 
d="M309 222q32 -38 32 -88v-1q0 -65 -43.5 -104t-112.5 -39q-50 0 -93 17v112q51 -23 86 -23q22 0 35 10.5t13 26.5v1q0 19 -17 34t-70 45q-41 23 -62.5 38.5t-40.5 44.5t-19 64v2q0 36 22.5 67.5t61.5 48.5q-32 38 -32 88v1q0 65 43.5 104t112.5 39q50 0 93 -17v-112
q-51 23 -86 23q-22 0 -35 -10.5t-13 -26.5v-1q0 -19 17 -34t70 -45q41 -23 62.5 -38.5t40.5 -44.5t19 -64v-2q0 -36 -22.5 -67.5t-61.5 -48.5zM252 272q27 22 27 51v2q0 19 -20.5 39.5t-87.5 56.5l-13 7q-27 -22 -27 -51v-2q0 -19 20.5 -39.5t87.5 -56.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="500" 
d="M268 578v108h106v-108h-106zM126 578v108h106v-108h-106z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="522" 
d="M261 -10q-106 0 -171.5 93t-65.5 250v34q0 157 65.5 250t171.5 93t171.5 -93t65.5 -250v-34q0 -157 -65.5 -250t-171.5 -93zM261 26q92 0 145.5 83t53.5 223v36q0 140 -53.5 223t-145.5 83t-145.5 -83t-53.5 -223v-36q0 -140 53.5 -223t145.5 -83zM272 151
q-49 0 -79 47.5t-30 143.5v18q0 96 31 143.5t82 47.5q26 0 44 -9v-72q-23 13 -37 13q-50 0 -50 -122v-20q0 -71 12.5 -96.5t38.5 -25.5q17 0 37 13v-69q-20 -12 -49 -12z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="250" 
d="M93 402q-25 0 -40.5 23.5t-15.5 63.5v9q0 93 65 93q20 0 40 -11v17q0 46 -42 46q-17 0 -49 -14v62q29 13 62 13q50 0 73 -31q20 -26 20 -72v-194h-65v32q-19 -37 -48 -37zM32 302v48h186v-48h-186zM120 457q9 0 15.5 7.5t6.5 20.5v50q-15 6 -21 6q-22 0 -22 -39v-7
q0 -38 21 -38z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="454" 
d="M167 43l-144 221v8l144 221l82 -48l-98 -176l101 -176zM350 43l-144 221v8l144 221l82 -48l-98 -176l101 -176z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="312" 
d="M156 355q-57 0 -92 47.5t-35 125.5v16q0 78 35 125.5t92 47.5t92 -47.5t35 -125.5v-16q0 -78 -35 -125.5t-92 -47.5zM156 378q47 0 74.5 41t27.5 109v16q0 68 -27.5 109t-74.5 41t-74.5 -41t-27.5 -109v-16q0 -68 27.5 -109t74.5 -41zM108 443v192h41q29 0 45.5 -15.5
t16.5 -45.5v-3q0 -34 -26 -51l29 -77h-37l-25 66h-10v-66h-34zM142 540h7q26 0 26 31v2q0 30 -27 30h-6v-63z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="500" 
d="M134 578v78h232v-78h-232z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="274" 
d="M137 362q-51 0 -81.5 45.5t-30.5 120.5v12q0 75 30.5 120.5t81.5 45.5t81.5 -45.5t30.5 -120.5v-12q0 -75 -30.5 -120.5t-81.5 -45.5zM137 438q35 0 35 90v12q0 90 -35 90t-35 -90v-12q0 -90 35 -90z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="380" 
d="M358 295h-113v-122h-110v122h-113v114h113v122h110v-122h113v-114zM356 0h-332v107h332v-107z" />
    <glyph glyph-name="two.sup" unicode="&#xb2;" 
d="M28 461v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185z" />
    <glyph glyph-name="three.sup" unicode="&#xb3;" 
d="M106 456q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="500" 
d="M156 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="360" 
d="M197 0v224q-76 8 -129.5 68t-53.5 170v2q0 122 64 179t167 57h74v-700h-122z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="238" 
d="M50 240v139h138v-139h-138z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="500" 
d="M251 -127l-100 38l73 109h75z" />
    <glyph glyph-name="one.sup" unicode="&#xb9;" horiz-adv-x="195" 
d="M149 461h-79v271l-52 -24v75l75 31h56v-353z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="250" 
d="M125 401q-41 0 -66 37t-25 106v18q0 69 25 106t66 37t66 -37t25 -106v-18q0 -69 -25 -106t-66 -37zM35 302v48h180v-48h-180zM125 458q26 0 26 85v20q0 85 -26 85t-26 -85v-20q0 -85 26 -85z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="454" 
d="M287 493l144 -221v-8l-144 -221l-82 48l98 176l-101 176zM104 493l144 -221v-8l-144 -221l-82 48l98 176l-101 176z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="495" 
d="M152 350h-79v271l-52 -24v75l75 31h56v-353zM65 0l150 364l131 336h63l-150 -364l-131 -336h-63zM444 64v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM380 129v140h-7l-53 -140h60z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="495" 
d="M276 0v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185zM152 350h-79v271l-52 -24v75l75 31h56v-353zM50 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="540" 
d="M107 345q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31zM110 0l150 364l131 336h63l-150 -364l-131 -336h-63zM495 64v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM431 129
v140h-7l-53 -140h60z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="320" 
d="M112 561v139h138v-139h-138zM191 -5q-79 0 -128 50.5t-49 128.5v9q0 126 117 179l12 120h80l11 -175l-4 -5q-44 -14 -70 -43.5t-26 -70.5v-2q0 -34 23 -56.5t58 -22.5q46 0 85 24v-110q-54 -26 -109 -26z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM250 740h-88l-99 77l101 50z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM148 740l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM77 740l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM265 739q-22 0 -62 19q-34 16 -43 16q-10 0 -15 -8t-11 -31l-69 17q10 60 27 84.5t47 24.5q24 0 66 -21q32 -14 39 -14q10 0 15 8t11 31l69 -17q-10 -60 -27 -84.5t-47 -24.5z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM220 743v108h108v-108h-108zM70 743v108h108v-108h-108z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="398" 
d="M1 0l128 679q-16 22 -16 51q0 36 25.5 60t61.5 24t61.5 -24t25.5 -60q0 -31 -19 -53l129 -677h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM200 690q17 0 28 11.5t11 28.5t-11 28.5t-28 11.5t-28 -11.5t-11 -28.5t11 -28.5t28 -11.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="556" 
d="M0 0l184 700h346v-116h-146v-174h129v-116h-129v-178h149v-116h-271v139h-112l-32 -139h-118zM176 249h86v325h-9z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="336" 
d="M202 -127l-100 38l62 93q-140 51 -140 334v24q0 175 56.5 259t148.5 84q45 0 80 -16v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-28 -17 -69 -20z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM222 735h-88l-99 77l101 50z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM120 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM49 735l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM192 738v108h108v-108h-108zM42 738v108h108v-108h-108z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM150 735h-88l-99 77l101 50z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM48 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM-23 735l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM120 738v108h108v-108h-108zM-30 738v108h108v-108h-108z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="427" 
d="M44 0v297h-36v110h36v293h114q118 0 181.5 -85.5t63.5 -252.5v-24q0 -167 -63.5 -252.5t-181.5 -85.5h-114zM220 297h-54v-185h5q108 0 108 226v24q0 226 -108 226h-5v-181h54v-110z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="428" 
d="M36 0v700h113l126 -362v362h117v-700h-107l-132 379v-379h-117zM280 729q-22 0 -62 19q-34 16 -43 16q-10 0 -15 -8t-11 -31l-69 17q10 60 27 84.5t47 24.5q24 0 66 -21q32 -14 39 -14q10 0 15 8t11 31l69 -17q-10 -60 -27 -84.5t-47 -24.5z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM261 740h-88l-99 77
l101 50z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM159 740l86 127
l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM88 740l74 119h96
l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM276 739
q-22 0 -62 19q-34 16 -43 16q-10 0 -15 -8t-11 -31l-69 17q10 60 27 84.5t47 24.5q24 0 66 -21q32 -14 39 -14q10 0 15 8t11 31l69 -17q-10 -60 -27 -84.5t-47 -24.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM231 743v108h108
v-108h-108zM81 743v108h108v-108h-108z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="380" 
d="M277 169l-87 102l-88 -102l-86 70l102 113l-102 113l87 70l87 -102l88 102l86 -70l-102 -113l102 -113z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="420" 
d="M321 705h69l-39 -92q45 -88 45 -245v-36q0 -168 -50 -255t-136 -87q-57 0 -98 37l-13 -32h-69l38 92q-44 88 -44 245v36q0 168 50 255t136 87q56 0 97 -38zM146 332q0 -40 1 -57l111 268q-15 57 -48 57q-64 0 -64 -232v-36zM210 100q64 0 64 232v36q0 11 -0.5 29.5
t-0.5 27.5l-112 -269q16 -56 49 -56z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM259 730h-88l-99 77l101 50z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM157 730l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM86 730l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM229 733v108h108v-108h-108zM79 733v108h108v-108h-108z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="382" 
d="M130 0v276l-130 424h126l66 -262l67 262h123l-130 -424v-276h-122zM140 730l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="385" 
d="M36 0v700h122v-96h1q87 0 149.5 -58t62.5 -178v-2q0 -114 -57 -174.5t-155 -67.5h-1v-124h-122zM158 237h1q41 2 65.5 33t24.5 92v2q0 127 -87 127h-4v-254z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="402" 
d="M199 -2v101q66 9 66 106v3q0 92 -66 110v86q52 35 52 116v6q0 78 -49 78q-51 0 -51 -102v-502h-118v498q0 95 45 152t127 57q75 0 117 -47.5t42 -117.5v-8q0 -106 -77 -160q95 -47 95 -166v-7q0 -94 -46.5 -147t-136.5 -56z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M139 580l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M125 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M50 580l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M238 579q-19 0 -63 20q-32 15 -42 15q-11 0 -16.5 -8t-11.5 -31l-65 16q10 58 26.5 82t45.5 24q19 0 63 -20q32 -15 42 -15q11 0 16.5 8t11.5 31l65 -16q-10 -58 -26.5 -82t-45.5 -24z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M190 583v108h106v-108h-106zM48 583v108h106v-108h-106z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M172 574q-37 0 -62 25.5t-25 64.5t25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM172 619q17 0 28 13t11 32t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="531" 
d="M131 -9q-55 0 -86 42t-31 116v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-33 0 -89 -25v112q52 23 112 23q78 0 116 -59q36 61 96 61q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41q-96 0 -140 101
q-14 -45 -44.5 -72.5t-69.5 -27.5zM318 306h87q0 133 -41 133q-43 0 -46 -133zM169 90q16 0 32 17.5t24 51.5q-7 35 -8 70q-25 15 -50 15q-40 0 -40 -71v-14q0 -69 42 -69z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="286" 
d="M171 -127l-100 38l60 90q-108 47 -108 250v32q0 125 47 193t122 68q43 0 74 -14v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-30 -17 -58 -21z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM148 580l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM134 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM59 580l78 139h88l78 -139h-77l-46 62l-46 -62h-75z
" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM199 583v108h106v-108h-106zM57 583v108h106v-108
h-106z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM60 575l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM46 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM-29 575l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM111 578v108h106v-108h-106zM-31 578v108h106v-108h-106z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="378" 
d="M328 635l-49 -18q74 -143 74 -315v-28q0 -142 -46 -213t-123 -71q-72 0 -116.5 69.5t-44.5 190.5v37q0 114 39 181.5t89 67.5q32 0 63 -31q-19 49 -33 75l-68 -25l-28 72l54 20q-21 29 -46 58h129l16 -21l62 23zM187 94q48 0 48 157v34q0 157 -48 157q-46 0 -46 -157v-34
q0 -157 46 -157z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="379" 
d="M33 0v536h118v-64q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118zM257 579q-19 0 -63 20q-32 15 -42 15q-11 0 -16.5 -8t-11.5 -31l-65 16q10 58 26.5 82t45.5 24q19 0 63 -20q32 -15 42 -15q11 0 16.5 8
t11.5 31l65 -16q-10 -58 -26.5 -82t-45.5 -24z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM156 580l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM142 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM67 580l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM255 579q-19 0 -63 20q-32 15 -42 15q-11 0 -16.5 -8t-11.5 -31l-65 16q10 58 26.5 82
t45.5 24q19 0 63 -20q32 -15 42 -15q11 0 16.5 8t11.5 31l65 -16q-10 -58 -26.5 -82t-45.5 -24z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM207 583v108h106v-108h-106zM65 583v108h106v-108h-106z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="380" 
d="M133 458v116h114v-116h-114zM356 297h-332v110h332v-110zM133 130v116h114v-116h-114z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="378" 
d="M295 542h69l-44 -84q35 -68 35 -175v-32q0 -125 -46 -193t-120 -68q-53 0 -90 33l-16 -31h-69l44 84q-35 68 -35 175v32q0 125 46 193t120 68q51 0 90 -33zM134 249v-26l94 181q-13 37 -39 37q-55 0 -55 -156v-36zM189 93q25 0 40 40t15 116v36q0 17 -1 26l-94 -181
q15 -37 40 -37z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM155 570l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM141 570l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM66 570l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM206 574v108h106v-108h-106zM64 574v108h106v-108h-106z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="362" 
d="M179 220l56 316h116l-148 -666h-115l34 149l-111 517h117zM134 570l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="384" 
d="M33 -130v835h118v-236q12 22 21 35.5t29 27.5t44 14q114 0 114 -254v-48q0 -254 -114 -254q-24 0 -44 14t-28.5 26t-21.5 35v-195h-118zM197 103q44 0 44 147v36q0 147 -44 147q-47 0 -47 -147v-36q0 -147 47 -147z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="362" 
d="M179 220l56 316h116l-148 -666h-115l34 149l-111 517h117zM57 573v108h106v-108h-106zM199 573v108h106v-108h-106z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM78 743v79h242v-79h-242z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M56 583v78h232v-78h-232z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="398" 
d="M1 0l133 702h130l133 -702h-122l-23 139h-111l-22 -139h-118zM159 249h75l-37 231zM199 734q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="350" 
d="M115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z
M172 574q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="398" 
d="M403 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-26l-23 139h-111l-22 -139h-118l133 702h130l133 -702h-14q-21 -28 -21 -51q0 -31 41 -28zM159 249h75l-37 231z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="350" 
d="M325 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-20v57q-31 -66 -88 -66q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-16q-21 -28 -21 -51q0 -31 41 -28z
M164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="336" 
d="M221 -5q-89 0 -143 84.5t-54 258.5v24q0 175 56.5 259t148.5 84q45 0 80 -16v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-37 -21 -90 -21zM152 740l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="286" 
d="M189 -10q-77 0 -121.5 67.5t-44.5 193.5v32q0 125 47 193t122 68q43 0 74 -14v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-40 -22 -78 -22zM127 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="336" 
d="M221 -5q-89 0 -143 84.5t-54 258.5v24q0 175 56.5 259t148.5 84q45 0 80 -16v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-37 -21 -90 -21zM148 743v108h110v-108h-110z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="286" 
d="M189 -10q-77 0 -121.5 67.5t-44.5 193.5v32q0 125 47 193t122 68q43 0 74 -14v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-40 -22 -78 -22zM120 583v108h108v-108h-108z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="336" 
d="M221 -5q-89 0 -143 84.5t-54 258.5v24q0 175 56.5 259t148.5 84q45 0 80 -16v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-37 -21 -90 -21zM325 859l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="286" 
d="M189 -10q-77 0 -121.5 67.5t-44.5 193.5v32q0 125 47 193t122 68q43 0 74 -14v-121q-33 20 -59 20q-64 0 -64 -144v-36q0 -144 66 -144q23 0 58 23v-116q-40 -22 -78 -22zM296 719l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="419" 
d="M36 0v700h114q118 0 181.5 -85.5t63.5 -252.5v-24q0 -167 -63.5 -252.5t-181.5 -85.5h-114zM163 588h-5v-476h5q108 0 108 226v24q0 226 -108 226zM325 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v234h118v-705h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM374 560l10 145h79v-2l-38 -143h-51z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="427" 
d="M44 0v297h-36v110h36v293h114q118 0 181.5 -85.5t63.5 -252.5v-24q0 -167 -63.5 -252.5t-181.5 -85.5h-114zM220 297h-54v-185h5q108 0 108 226v24q0 226 -108 226h-5v-181h54v-110z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="384" 
d="M154 579v80h79v46h118v-46h33v-82h-33v-577h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v108h-79zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM50 738v79h242v-79h-242z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM65 583v78h232v-78h-232z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM171 729q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM181 574q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47
t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM116 738v108h110v-108h-110z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM127 583v108h108v-108h-108z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-19q-21 -28 -21 -51q0 -31 41 -28v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-170z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="350" 
d="M313 139l9 -106q-49 -53 -49 -84t41 -28v-68q-20 -4 -41 -4q-97 0 -97 71q0 37 29 72h-1q-79 0 -131.5 69t-52.5 193v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30zM137 306h87q0 133 -41 133q-43 0 -46 -133z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="330" 
d="M36 0v700h268v-116h-146v-174h129v-116h-129v-178h149v-116h-271zM293 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="350" 
d="M204 -10q-79 0 -131.5 69.5t-52.5 194.5v26q0 124 46.5 194t116.5 70q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41zM137 306h87q0 133 -41 133q-43 0 -46 -133zM303 719l-78 -139h-88l-78 139h77l46 -62l46 62h75z
" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="405" 
d="M247 -10q-99 0 -161 86t-62 262v24q0 168 63.5 258t170.5 90q51 0 93 -20v-127q-50 30 -89 30q-54 0 -84 -56.5t-30 -169.5v-34q0 -230 104 -230q5 0 11 1v184h-60v109h171v-377q-69 -30 -127 -30zM233 734q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70
q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="384" 
d="M163 -132q-57 0 -116 17v109q61 -22 100 -22q86 0 86 84v53q-11 -21 -21 -35.5t-30 -28t-44 -13.5q-113 0 -113 254v6q0 254 113 254q24 0 44 -13t29.5 -26.5t21.5 -35.5v65h118v-489q0 -87 -46 -133t-142 -46zM187 145q47 0 47 143v2q0 70 -12.5 106.5t-34.5 36.5
q-44 0 -44 -143v-2q0 -143 44 -143zM200 574q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="405" 
d="M247 -10q-99 0 -161 86t-62 262v24q0 168 63.5 258t170.5 90q51 0 93 -20v-127q-50 30 -89 30q-54 0 -84 -56.5t-30 -169.5v-34q0 -230 104 -230q5 0 11 1v184h-60v109h171v-377q-69 -30 -127 -30zM178 743v108h110v-108h-110z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="384" 
d="M163 -132q-57 0 -116 17v109q61 -22 100 -22q86 0 86 84v53q-11 -21 -21 -35.5t-30 -28t-44 -13.5q-113 0 -113 254v6q0 254 113 254q24 0 44 -13t29.5 -26.5t21.5 -35.5v65h118v-489q0 -87 -46 -133t-142 -46zM187 145q47 0 47 143v2q0 70 -12.5 106.5t-34.5 36.5
q-44 0 -44 -143v-2q0 -143 44 -143zM146 583v108h108v-108h-108z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="405" 
d="M247 -10q-99 0 -161 86t-62 262v24q0 168 63.5 258t170.5 90q51 0 93 -20v-127q-50 30 -89 30q-54 0 -84 -56.5t-30 -169.5v-34q0 -230 104 -230q5 0 11 1v184h-60v109h171v-377q-69 -30 -127 -30zM179 -230l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81
q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="384" 
d="M163 -132q-57 0 -116 17v109q61 -22 100 -22q86 0 86 84v53q-11 -21 -21 -35.5t-30 -28t-44 -13.5q-113 0 -113 254v6q0 254 113 254q24 0 44 -13t29.5 -26.5t21.5 -35.5v65h118v-489q0 -87 -46 -133t-142 -46zM187 145q47 0 47 143v2q0 70 -12.5 106.5t-34.5 36.5
q-44 0 -44 -143v-2q0 -143 44 -143zM253 766l5 -40q-32 -4 -43.5 -15.5t-10.5 -37.5h48v-90h-106v81q0 51 26.5 74.5t80.5 27.5z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="438" 
d="M8 517v104h36v79h122v-81h106v81h122v-79h36v-104h-36v-517h-122v294h-106v-294h-122v517h-36zM166 410h106v109h-106v-109z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="379" 
d="M230 579h-79v-107q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118v577h-33v82h33v46h118v-46h79v-80z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM-22 738v79h242v-79h-242z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM-23 578v78h232v-78h-232z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM99 729q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118zM93 569q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-17q-21 -28 -21 -51q0 -31 41 -28v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-23z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-15q-21 -28 -21 -51q0 -31 41 -28v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-21zM33 587v118h120v-118h-120z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="198" 
d="M38 0v700h122v-700h-122zM44 738v108h110v-108h-110z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="186" 
d="M34 0v536h118v-536h-118z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="403" 
d="M36 0v700h122v-300l101 300h134l-117 -296l119 -404h-132l-77 268l-28 -78v-190h-122zM151 -220l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="357" 
d="M33 0v705h118v-379l77 210h126l-92 -217l88 -319h-129l-48 206l-22 -59v-147h-118zM129 -220l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="300" 
d="M36 0v700h122v-584h129v-116h-251zM62 733l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="186" 
d="M34 0v705h118v-705h-118zM52 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="300" 
d="M36 0v700h122v-584h129v-116h-251zM107 -225l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="186" 
d="M34 0v705h118v-705h-118zM39 -225l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="300" 
d="M36 0v700h122v-584h129v-116h-251zM181 555l10 145h79v-2l-38 -143h-51z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="186" 
d="M34 0v705h118v-705h-118zM175 560l10 145h79v-2l-38 -143h-51z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="300" 
d="M199 307v108h98v-108h-98zM36 0v700h122v-584h129v-116h-251z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="274" 
d="M186 304v108h88v-108h-88zM34 0v705h118v-705h-118z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="327" 
d="M236 370l-70 -40v-214h129v-116h-251v259l-36 -21v118l36 20v324h122v-253l70 41v-118z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="222" 
d="M216 367l-46 -28v-339h-118v268l-46 -28v114l46 27v324h118v-252l46 28v-114z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="428" 
d="M36 0v700h113l126 -362v362h117v-700h-107l-132 379v-379h-117zM163 730l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="379" 
d="M33 0v536h118v-64q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118zM144 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="428" 
d="M36 0v700h113l126 -362v362h117v-700h-107l-132 379v-379h-117zM160 -220l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="379" 
d="M33 0v536h118v-64q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118zM137 -220l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="428" 
d="M36 0v700h113l126 -362v362h117v-700h-107l-132 379v-379h-117zM336 849l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="379" 
d="M33 0v536h118v-64q38 74 95 74q50 0 76.5 -38.5t26.5 -101.5v-406h-118v379q0 61 -39 61q-18 0 -29.5 -16t-11.5 -45v-379h-118zM313 719l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM89 743v79h242v-79
h-242z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM73 583v78h232v-78h-232z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM210 734
q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM189 574q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z
" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="420" 
d="M210 -10q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -168 -50 -255t-136 -87zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5t33 -19zM222 740l44 64
l-80 -64h-82l86 127l86 -48l32 48l91 -50l-95 -77h-82z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="378" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t120 -68t46 -193v-32q0 -125 -46 -193t-120 -68zM189 95q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154zM88 580l86 147l83 -47l28 47l85 -48l-96 -99h-75l55 93l-91 -93h-75z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="556" 
d="M269 0q-118 0 -181.5 85.5t-63.5 252.5v24q0 167 63.5 252.5t181.5 85.5h261v-116h-146v-174h129v-116h-129v-178h149v-116h-264zM256 112h5v476h-5q-108 0 -108 -226v-24q0 -226 108 -226z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="577" 
d="M189 -10q-74 0 -120 68t-46 193v32q0 125 46 193t120 68t113 -71q38 71 108 71q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41q-82 0 -127 75q-38 -75 -115 -75zM364 306h87q0 133 -41 133q-43 0 -46 -133zM189 95
q48 0 48 154v36q0 154 -48 154t-48 -154v-36q0 -154 48 -154z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="398" 
d="M36 0v700h126q102 0 158 -56q58 -58 58 -167v-9q0 -122 -82 -184l94 -284h-130l-79 244h-23v-244h-122zM158 354h6q92 0 92 113v8q0 111 -95 111h-3v-232zM142 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="250" 
d="M33 0v536h118v-67q27 77 86 73v-142h-2q-84 0 -84 -114v-286h-118zM82 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="398" 
d="M36 0v700h126q102 0 158 -56q58 -58 58 -167v-9q0 -122 -82 -184l94 -284h-130l-79 244h-23v-244h-122zM158 354h6q92 0 92 113v8q0 111 -95 111h-3v-232zM139 -220l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="250" 
d="M33 0v536h118v-67q27 77 86 73v-142h-2q-84 0 -84 -114v-286h-118zM44 -225l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="398" 
d="M36 0v700h126q102 0 158 -56q58 -58 58 -167v-9q0 -122 -82 -184l94 -284h-130l-79 244h-23v-244h-122zM158 354h6q92 0 92 113v8q0 111 -95 111h-3v-232zM315 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="250" 
d="M33 0v536h118v-67q27 77 86 73v-142h-2q-84 0 -84 -114v-286h-118zM251 714l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="325" 
d="M145 -5q-64 0 -124 29v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 82 48.5 132t124.5 50q55 0 105 -22v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104q0 -81 -50 -132t-123 -51z
M117 740l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="284" 
d="M126 -10q-52 0 -109 25v116q67 -35 101 -35q44 0 44 43q0 17 -11.5 35t-48.5 54q-46 45 -68.5 83.5t-22.5 85.5v2q0 66 41.5 105.5t110.5 39.5q46 0 88 -14v-116q-46 24 -81 24q-21 0 -33 -10t-12 -27q0 -16 12 -32.5t52 -56.5q49 -48 68 -85t19 -83v-2
q0 -69 -42.5 -110.5t-107.5 -41.5zM98 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="325" 
d="M162 -127l-100 38l57 85q-54 6 -98 28v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 82 48.5 132t124.5 50q55 0 105 -22v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104
q0 -63 -31 -109t-82 -64z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="284" 
d="M140 -127l-100 38l54 82q-44 7 -77 22v116q67 -35 101 -35q44 0 44 43q0 17 -11.5 35t-48.5 54q-46 45 -68.5 83.5t-22.5 85.5v2q0 66 41.5 105.5t110.5 39.5q46 0 88 -14v-116q-46 24 -81 24q-21 0 -33 -10t-12 -27q0 -16 12 -32.5t52 -56.5q49 -48 68 -85t19 -83v-2
q0 -53 -26 -90.5t-69 -52.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="325" 
d="M145 -5q-64 0 -124 29v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 82 48.5 132t124.5 50q55 0 105 -22v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104q0 -81 -50 -132t-123 -51z
M290 859l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="284" 
d="M126 -10q-52 0 -109 25v116q67 -35 101 -35q44 0 44 43q0 17 -11.5 35t-48.5 54q-46 45 -68.5 83.5t-22.5 85.5v2q0 66 41.5 105.5t110.5 39.5q46 0 88 -14v-116q-46 24 -81 24q-21 0 -33 -10t-12 -27q0 -16 12 -32.5t52 -56.5q49 -48 68 -85t19 -83v-2
q0 -69 -42.5 -110.5t-107.5 -41.5zM267 719l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="324" 
d="M101 0v584h-86v116h294v-116h-86v-584h-122zM108 -225l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="244" 
d="M157 -7q-110 0 -110 99v348h-32v96h32v137h118v-137h63v-96h-63v-308q0 -33 33 -33q14 0 29 6v-94q-32 -18 -70 -18zM79 -230l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="324" 
d="M101 0v584h-86v116h294v-116h-86v-584h-122zM284 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="244" 
d="M157 -7q-110 0 -110 99v348h-32v96h32v137h118v-137h63v-96h-63v-308q0 -33 33 -33q14 0 29 6v-94q-32 -18 -70 -18zM188 560l10 145h79v-2l-38 -143h-51z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM87 733v79h242v-79h-242z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM72 573v78h232v-78h-232z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM208 719q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM188 564q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM208 714q-36 0 -61.5 24t-25.5 60t25.5 60t61.5 24t61.5 -24t25.5 -60t-25.5 -60t-61.5 -24zM208 758q17 0 28 11.5t11 28.5t-11 28.5t-28 11.5
t-28 -11.5t-11 -28.5t11 -28.5t28 -11.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM188 564q-37 0 -62 25.5t-25 64.5t25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM188 609q17 0 28 13t11 32
t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="416" 
d="M208 -9q-79 0 -126.5 53.5t-47.5 159.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -106 -47.5 -159.5t-126.5 -53.5zM220 730l44 64l-80 -64h-82l86 127l86 -48l32 48l91 -50l-95 -77h-82z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="379" 
d="M133 -10q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-118v64q-38 -74 -95 -74zM87 570l86 147l83 -47l28 47l85 -48l-96 -99h-75l55 93l-91 -93h-75z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="416" 
d="M271 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 39 32 76q-60 13 -95.5 65.5t-35.5 142.5v496h122v-508q0 -87 52 -87t52 87v508h122v-496q0 -92 -36 -144t-98 -65q-18 -26 -18 -46q0 -31 41 -28z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="379" 
d="M350 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-20v64q-38 -74 -95 -74q-50 0 -76.5 38.5t-26.5 101.5v406h118v-379q0 -61 39 -61q18 0 29.5 16t11.5 45v379h118v-536h-16q-21 -28 -21 -51q0 -31 41 -28z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="592" 
d="M109 -2l-106 702h125l51 -429l61 429h113l60 -429l52 429h122l-106 -702h-128l-58 415l-58 -415h-128zM173 735l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="526" 
d="M103 -2l-92 538h119l41 -309l47 309h92l47 -309l41 309h117l-91 -538h-112l-49 301l-48 -301h-112zM141 575l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="382" 
d="M130 0v276l-130 424h126l66 -262l67 262h123l-130 -424v-276h-122zM69 730l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="362" 
d="M179 220l56 316h116l-148 -666h-115l34 149l-111 517h117zM59 570l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="382" 
d="M130 0v276l-130 424h126l66 -262l67 262h123l-130 -424v-276h-122zM212 733v108h108v-108h-108zM62 733v108h108v-108h-108z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="340" 
d="M23 0v97l161 488h-155v115h289v-97l-161 -488h161v-115h-295zM122 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="290" 
d="M12 0v84l141 352h-136v100h262v-84l-141 -352h141v-100h-267zM102 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="340" 
d="M23 0v97l161 488h-155v115h289v-97l-161 -488h161v-115h-295zM118 738v108h110v-108h-110z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="290" 
d="M12 0v84l141 352h-136v100h262v-84l-141 -352h141v-100h-267zM95 578v108h108v-108h-108z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="340" 
d="M23 0v97l161 488h-155v115h289v-97l-161 -488h161v-115h-295zM295 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="290" 
d="M12 0v84l141 352h-136v100h262v-84l-141 -352h141v-100h-267zM271 714l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="333" 
d="M66 -5q-27 0 -55 6v106q12 -3 26 -3q39 0 43 51l15 207h-45v96h52l7 97q5 73 42.5 111.5t98.5 38.5q42 0 72 -8v-107q-22 6 -39 6q-53 0 -57 -63l-6 -75h81v-96h-88l-16 -222q-11 -145 -131 -145z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="398" 
d="M1 0l128 679q-16 22 -16 51q0 36 25.5 60t61.5 24t61.5 -24t25.5 -60q0 -31 -19 -53l129 -677h-122l-23 139h-111l-22 -139h-118zM155 830l80 107l101 -50l-88 -57h-93zM159 249h75l-37 231zM200 690q17 0 28 11.5t11 28.5t-11 28.5t-28 11.5t-28 -11.5t-11 -28.5
t11 -28.5t28 -11.5z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="350" 
d="M129 773l76 112l93 -44l-85 -68h-84zM115 -9q-45 0 -73 43t-28 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-39 0 -89 -25v112q52 23 107 23q83 0 126 -43q48 -48 48 -143v-356h-118v57q-31 -66 -88 -66zM164 90q16 0 28 13.5t12 37.5v92
q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69zM173 574q-37 0 -62 25.5t-25 64.5t25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM173 619q17 0 28 13t11 32t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="556" 
d="M0 0l184 700h346v-116h-146v-174h129v-116h-129v-178h149v-116h-271v139h-112l-32 -139h-118zM176 249h86v325h-9zM263 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="531" 
d="M131 -9q-55 0 -86 42t-31 116v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-33 0 -89 -25v112q52 23 112 23q78 0 116 -59q36 61 96 61q71 0 111.5 -69.5t40.5 -199.5v-62h-197q9 -113 81 -113q25 0 44 9t50 30l9 -108q-62 -41 -118 -41q-96 0 -140 101
q-14 -45 -44.5 -72.5t-69.5 -27.5zM318 306h87q0 133 -41 133q-43 0 -46 -133zM169 90q16 0 32 17.5t24 51.5q-7 35 -8 70q-25 15 -50 15q-40 0 -40 -71v-14q0 -69 42 -69zM218 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="420" 
d="M321 705h69l-39 -92q45 -88 45 -245v-36q0 -168 -50 -255t-136 -87q-57 0 -98 37l-13 -32h-69l38 92q-44 88 -44 245v36q0 168 50 255t136 87q56 0 97 -38zM210 100q64 0 64 232v36q0 11 -0.5 29.5t-0.5 27.5l-112 -269q16 -56 49 -56zM146 332q0 -40 1 -57l111 268
q-15 57 -48 57q-64 0 -64 -232v-36zM159 740l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="378" 
d="M295 542h69l-44 -84q35 -68 35 -175v-32q0 -125 -46 -193t-120 -68q-53 0 -90 33l-16 -31h-69l44 84q-35 68 -35 175v32q0 125 46 193t120 68q51 0 90 -33zM140 580l86 147l93 -49l-99 -98h-80zM134 249v-26l94 181q-13 37 -39 37q-55 0 -55 -156v-36zM189 93q25 0 40 40
t15 116v36q0 17 -1 26l-94 -181q15 -37 40 -37z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="325" 
d="M145 -5q-64 0 -124 29v125q67 -39 108 -39q30 0 48.5 18t18.5 51q0 47 -67 120q-68 74 -93 120t-25 104q0 82 48.5 132t124.5 50q55 0 105 -22v-125q-55 32 -93 32q-27 0 -45 -16.5t-18 -46.5q0 -22 13 -44.5t55 -71.5q71 -82 94 -129t23 -104q0 -81 -50 -132t-123 -51z
M104 -230l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="284" 
d="M126 -10q-52 0 -109 25v116q67 -35 101 -35q44 0 44 43q0 17 -11.5 35t-48.5 54q-46 45 -68.5 83.5t-22.5 85.5v2q0 66 41.5 105.5t110.5 39.5q46 0 88 -14v-116q-46 24 -81 24q-21 0 -33 -10t-12 -27q0 -16 12 -32.5t52 -56.5q49 -48 68 -85t19 -83v-2
q0 -69 -42.5 -110.5t-107.5 -41.5zM84 -230l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="schwa" unicode="&#x259;" horiz-adv-x="350" 
d="M146 546q79 0 131.5 -69.5t52.5 -194.5v-26q0 -124 -46.5 -194t-116.5 -70q-71 0 -111.5 69.5t-40.5 199.5v62h197q-9 113 -81 113q-25 0 -44 -9t-50 -30l-9 108q62 41 118 41zM213 230h-87q0 -133 41 -133q43 0 46 133z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="500" 
d="M128 575l78 139h88l78 -139h-77l-46 62l-46 -62h-75z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="500" 
d="M372 714l-78 -139h-88l-78 139h77l46 -62l46 62h75z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="500" 
d="M250 569q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="500" 
d="M196 578v108h108v-108h-108z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="500" 
d="M250 569q-37 0 -62 25.5t-25 64.5t25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM250 614q17 0 28 13t11 32t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="500" 
d="M315 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 42 46 90h80q-29 -37 -29 -61q0 -31 41 -28z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="500" 
d="M313 574q-19 0 -63 20q-32 15 -42 15q-11 0 -16.5 -8t-11.5 -31l-65 16q10 58 26.5 82t45.5 24q19 0 63 -20q32 -15 42 -15q11 0 16.5 8t11.5 31l65 -16q-10 -58 -26.5 -82t-45.5 -24z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="500" 
d="M107 575l86 147l83 -47l28 47l85 -48l-96 -99h-75l55 93l-91 -93h-75z" />
    <glyph glyph-name="commaaccent" unicode="&#x326;" horiz-adv-x="500" 
d="M199 -225l-5 40q32 4 43.5 15.5t10.5 37.5h-48v90h106v-81q0 -51 -26.5 -74.5t-80.5 -27.5z" />
    <glyph glyph-name="baht" unicode="&#xe3f;" horiz-adv-x="392" 
d="M200 793h79l-14 -116q103 -42 103 -155v-5q0 -110 -101 -158q110 -48 110 -156v-9q0 -82 -53 -132.5t-142 -59.5l-14 -125h-79l14 123h-67v700h104q24 0 48 -3zM156 113h3q43 0 69.5 24t26.5 67v5q0 43 -26.5 67t-69.5 24h-3v-187zM156 406h2q42 0 66 23.5t24 68.5v2
q0 87 -90 87h-2v-181z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="592" 
d="M109 -2l-106 702h125l51 -429l61 429h113l60 -429l52 429h122l-106 -702h-128l-58 415l-58 -415h-128zM346 735h-88l-99 77l101 50z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="526" 
d="M103 -2l-92 538h119l41 -309l47 309h92l47 -309l41 309h117l-91 -538h-112l-49 301l-48 -301h-112zM230 575l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="592" 
d="M109 -2l-106 702h125l51 -429l61 429h113l60 -429l52 429h122l-106 -702h-128l-58 415l-58 -415h-128zM244 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="526" 
d="M103 -2l-92 538h119l41 -309l47 309h92l47 -309l41 309h117l-91 -538h-112l-49 301l-48 -301h-112zM216 575l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="592" 
d="M109 -2l-106 702h125l51 -429l61 429h113l60 -429l52 429h122l-106 -702h-128l-58 415l-58 -415h-128zM316 738v108h108v-108h-108zM166 738v108h108v-108h-108z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="526" 
d="M103 -2l-92 538h119l41 -309l47 309h92l47 -309l41 309h117l-91 -538h-112l-49 301l-48 -301h-112zM281 578v108h106v-108h-106zM139 578v108h106v-108h-106z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="382" 
d="M130 0v276l-130 424h126l66 -262l67 262h123l-130 -424v-276h-122zM242 730h-88l-99 77l101 50z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="362" 
d="M179 220l56 316h116l-148 -666h-115l34 149l-111 517h117zM148 570l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="enspace" unicode="&#x2002;" horiz-adv-x="500" 
 />
    <glyph glyph-name="emspace" unicode="&#x2003;" horiz-adv-x="1000" 
 />
    <glyph glyph-name="threeperemspace" unicode="&#x2004;" horiz-adv-x="333" 
 />
    <glyph glyph-name="fourperemspace" unicode="&#x2005;" horiz-adv-x="250" 
 />
    <glyph glyph-name="figurespace" unicode="&#x2007;" horiz-adv-x="360" 
 />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="133" 
 />
    <glyph glyph-name="hairspace" unicode="&#x200a;" horiz-adv-x="75" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="456" 
d="M39 251v118h378v-118h-378z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="674" 
d="M39 251v118h596v-118h-596z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="225" 
d="M40 402v114q0 80 38 128.5t112 58.5l10 -49q-51 -11 -69.5 -42.5t-14.5 -70.5h62v-139h-138z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="225" 
d="M35 399l-10 49q51 11 69.5 42.5t14.5 70.5h-62v139h138v-114q0 -80 -38 -128.5t-112 -58.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="250" 
d="M38 -162l-10 49q57 12 78.5 43t17.5 70h-62v139h138v-114q0 -167 -162 -187z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="415" 
d="M230 402v114q0 80 38 128.5t112 58.5l10 -49q-51 -11 -69.5 -42.5t-14.5 -70.5h62v-139h-138zM40 402v114q0 80 38 128.5t112 58.5l10 -49q-51 -11 -69.5 -42.5t-14.5 -70.5h62v-139h-138z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="415" 
d="M225 399l-10 49q51 11 69.5 42.5t14.5 70.5h-62v139h138v-114q0 -80 -38 -128.5t-112 -58.5zM35 399l-10 49q51 11 69.5 42.5t14.5 70.5h-62v139h138v-114q0 -80 -38 -128.5t-112 -58.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="450" 
d="M238 -162l-10 49q57 12 78.5 43t17.5 70h-62v139h138v-114q0 -167 -162 -187zM38 -162l-10 49q57 12 78.5 43t17.5 70h-62v139h138v-114q0 -167 -162 -187z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="238" 
d="M79 238l13 272l-74 -10v79l73 -11l-15 132h86l-15 -132l73 11v-79l-74 10l13 -272h-80z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="256" 
d="M85 0l15 131l-73 -10v79l74 -11l-15 161l15 160l-74 -10v79l73 -11l-15 132h86l-15 -132l73 11v-79l-74 10l14 -160l-14 -161l74 11v-79l-73 10l15 -131h-86z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="351" 
d="M175 203q-50 0 -80.5 37t-30.5 105v18q0 68 30.5 105t80.5 37t80.5 -37t30.5 -105v-18q0 -68 -30.5 -105t-80.5 -37z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="734" 
d="M546 0v139h138v-139h-138zM298 0v139h138v-139h-138zM50 0v139h138v-139h-138z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="829" 
d="M137 413q30 0 30 96v20q0 96 -30 96t-30 -96v-20q0 -96 30 -96zM137 332q-51 0 -81.5 47.5t-30.5 128.5v22q0 81 30.5 128.5t81.5 47.5t81.5 -47.5t30.5 -128.5v-22q0 -81 -30.5 -128.5t-81.5 -47.5zM485 700l-171 -364l-156 -336h-71l171 364l156 336h71zM433 75
q30 0 30 96v20q0 96 -30 96t-30 -96v-20q0 -96 30 -96zM433 -6q-51 0 -81.5 47.5t-30.5 128.5v22q0 81 30.5 128.5t81.5 47.5t81.5 -47.5t30.5 -128.5v-22q0 -81 -30.5 -128.5t-81.5 -47.5zM692 75q30 0 30 96v20q0 96 -30 96t-30 -96v-20q0 -96 30 -96zM692 -6
q-51 0 -81.5 47.5t-30.5 128.5v22q0 81 30.5 128.5t81.5 47.5t81.5 -47.5t30.5 -128.5v-22q0 -81 -30.5 -128.5t-81.5 -47.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="271" 
d="M167 43l-144 221v8l144 221l82 -48l-98 -176l101 -176z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="271" 
d="M104 493l144 -221v-8l-144 -221l-82 48l98 176l-101 176z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="60" 
d="M-142 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="zero.sup" unicode="&#x2070;" 
d="M120 456q-52 0 -76 42t-24 130v16q0 88 24 130t76 42t76 -42t24 -130v-16q0 -88 -24 -130t-76 -42zM120 747q-15 0 -20.5 -22t-5.5 -84v-10q0 -62 5.5 -84t20.5 -22t20.5 22t5.5 84v10q0 62 -5.5 84t-20.5 22z" />
    <glyph glyph-name="four.sup" unicode="&#x2074;" 
d="M201 525v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM137 590v140h-7l-53 -140h60z" />
    <glyph glyph-name="five.sup" unicode="&#x2075;" 
d="M105 456q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5z" />
    <glyph glyph-name="six.sup" unicode="&#x2076;" 
d="M122 456q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM123 523q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65z" />
    <glyph glyph-name="seven.sup" unicode="&#x2077;" 
d="M120 461h-78l91 278h-109v72h189v-53z" />
    <glyph glyph-name="eight.sup" unicode="&#x2078;" 
d="M120 456q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM120 761q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM120 615q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52z" />
    <glyph glyph-name="nine.sup" unicode="&#x2079;" 
d="M118 816q37 0 61 -24q39 -36 39 -140v-13q0 -183 -119 -183q-28 0 -61 18v73q29 -23 56 -23q45 0 48 65q-22 -13 -44 -13q-33 0 -56 26.5t-23 87.5q0 59 28.5 92.5t70.5 33.5zM120 640q15 0 23 10v32q0 67 -26 67q-27 0 -27 -55q0 -54 30 -54z" />
    <glyph glyph-name="zero.inf" unicode="&#x2080;" 
d="M120 -137q-52 0 -76 42t-24 130v16q0 88 24 130t76 42t76 -42t24 -130v-16q0 -88 -24 -130t-76 -42zM120 154q-15 0 -20.5 -22t-5.5 -84v-10q0 -62 5.5 -84t20.5 -22t20.5 22t5.5 84v10q0 62 -5.5 84t-20.5 22z" />
    <glyph glyph-name="one.inf" unicode="&#x2081;" horiz-adv-x="195" 
d="M149 -132h-79v271l-52 -24v75l75 31h56v-353z" />
    <glyph glyph-name="two.inf" unicode="&#x2082;" 
d="M28 -132v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185z" />
    <glyph glyph-name="three.inf" unicode="&#x2083;" 
d="M106 -137q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="four.inf" unicode="&#x2084;" 
d="M201 -68v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM137 -3v140h-7l-53 -140h60z" />
    <glyph glyph-name="five.inf" unicode="&#x2085;" 
d="M105 -137q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5z" />
    <glyph glyph-name="six.inf" unicode="&#x2086;" 
d="M122 -137q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM123 -70q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65z" />
    <glyph glyph-name="seven.inf" unicode="&#x2087;" 
d="M120 -132h-78l91 278h-109v72h189v-53z" />
    <glyph glyph-name="eight.inf" unicode="&#x2088;" 
d="M120 -137q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM120 168q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM120 22q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52z" />
    <glyph glyph-name="nine.inf" unicode="&#x2089;" 
d="M118 223q37 0 61 -24q39 -36 39 -140v-13q0 -183 -119 -183q-28 0 -61 18v73q29 -23 56 -23q45 0 48 65q-22 -13 -44 -13q-33 0 -56 26.5t-23 87.5q0 59 28.5 92.5t70.5 33.5zM120 47q15 0 23 10v32q0 67 -26 67q-27 0 -27 -55q0 -54 30 -54z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="334" 
d="M255 779h36l-8 -80q22 -8 26 -10v-126q-38 23 -68 23q-44 0 -68.5 -50t-24.5 -173v-26q0 -129 24 -176t71 -47q31 0 68 22v-120q-37 -21 -90 -21h-13l-12 -113h-36l13 119q-27 9 -51 27l-16 -146h-36l20 179q-66 89 -66 277v24q0 266 132 327l9 90h36l-8 -78q16 4 36 4
q12 0 18 -1z" />
    <glyph glyph-name="naira" unicode="&#x20a6;" horiz-adv-x="532" 
d="M520 192h-76v-192h-108l-69 198l-60 173v-371h-119v192h-76v104h76v112h-76v104h76v188h114l59 -169l64 -185v354h119v-188h76v-104h-76v-111h76v-105z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="627" 
d="M30 0v700h120q89 0 141 -55t52 -168v-9q0 -120 -73 -183l84 -285h-121l-72 247h-17v-247h-114zM482 -10q-55 0 -106 23v110q59 -30 99 -30q18 0 30 12t12 32v2q0 20 -10.5 38.5t-46.5 54.5q-44 44 -65 80.5t-21 84.5v2q0 67 39 106t106 39q50 0 86 -12v-110
q-41 19 -79 19q-20 0 -32 -11t-12 -28v-2q0 -18 11.5 -36t49.5 -56q47 -47 64.5 -82.5t17.5 -81.5v-2q0 -69 -40 -110.5t-103 -41.5zM144 351h5q37 0 59 28.5t22 89.5v8q0 115 -83 115h-3v-241z" />
    <glyph glyph-name="won" unicode="&#x20a9;" horiz-adv-x="685" 
d="M634 700l-29 -188h78v-104h-93l-17 -111h110v-105h-126l-29 -194h-128l-32 229l-26 186l-26 -186l-32 -229h-128l-30 194h-124v104h109l-17 112h-92v104h76l-28 188h125l24 -202l27 -227l33 232l28 197h113l28 -200l32 -229l52 429h122z" />
    <glyph glyph-name="sheqel" unicode="&#x20aa;" horiz-adv-x="454" 
d="M162 139l-17 183h60l13 -125q7 -74 57 -74h60v413h82v-536h-142q-47 0 -76.5 35t-36.5 104zM241 338q0 75 -49 75h-73v-413h-82v536h171q40 0 66.5 -34t26.5 -92v-196h-60v124z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="379" 
d="M354 694v-126q-38 23 -68 23q-67 0 -86 -113h101v-90h-108v-24v-28v-20h108v-90h-102q9 -66 31.5 -91.5t57.5 -25.5q31 0 68 22v-120q-37 -21 -90 -21q-74 0 -124 58.5t-67 177.5h-60v90h54v21v26v25h-54v90h62q17 117 70 174.5t127 57.5q45 0 80 -16z" />
    <glyph glyph-name="peso" unicode="&#x20b1;" horiz-adv-x="498" 
d="M79 0v403h-76v104h76v193h109q94 0 153.5 -48t70.5 -145h78v-104h-81q-14 -85 -68.5 -131t-138.5 -52h-1v-220h-122zM202 333q41 2 65.5 33t24.5 92v2q0 127 -87 127h-4v-254h1z" />
    <glyph glyph-name="published" unicode="&#x2117;" horiz-adv-x="522" 
d="M261 -10q-106 0 -171.5 93t-65.5 250v34q0 157 65.5 250t171.5 93t171.5 -93t65.5 -250v-34q0 -157 -65.5 -250t-171.5 -93zM261 26q92 0 145.5 83t53.5 223v36q0 140 -53.5 223t-145.5 83t-145.5 -83t-53.5 -223v-36q0 -140 53.5 -223t145.5 -83zM187 164v385h60
q57 0 90.5 -32t33.5 -97v-2q0 -63 -31 -96t-85 -37h-1v-121h-67zM254 347h1q49 3 49 69v1q0 70 -48 70h-2v-140z" />
    <glyph glyph-name="servicemark" unicode="&#x2120;" horiz-adv-x="441" 
d="M198 385v315h67l36 -105l36 105h67v-315h-62v191l-41 -120h-1l-39 120v-191h-63zM81 383q-37 0 -62 14v65q33 -19 55 -19q25 0 25 23q0 19 -27 46q-33 33 -45.5 52.5t-12.5 51.5q0 39 24 62.5t59 23.5t55 -10v-66q-26 15 -48 15q-10 0 -17 -6t-7 -16q0 -16 27 -44
q34 -34 46 -55t12 -51q0 -38 -24.5 -62t-59.5 -24z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="431" 
d="M188 385v315h67l36 -105l36 105h67v-315h-62v191l-41 -120h-1l-39 120v-191h-63zM54 385v253h-39v62h142v-62h-39v-253h-64z" />
    <glyph glyph-name="onethird" unicode="&#x2153;" horiz-adv-x="495" 
d="M368 -5q-44 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31zM152 350h-79v271l-52 -24v75l75 31h56v-353zM50 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="twothirds" unicode="&#x2154;" horiz-adv-x="540" 
d="M420 -5q-44 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31zM24 350v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98
l-60 -91h94v-72h-185zM98 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="onefifth" unicode="&#x2155;" horiz-adv-x="495" 
d="M357 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5zM152 350h-79v271l-52 -24v75l75 31h56
v-353zM45 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="twofifths" unicode="&#x2156;" horiz-adv-x="540" 
d="M24 350v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185zM103 0l150 364l131 336h63l-150 -364l-131 -336h-63zM414 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5
q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5z" />
    <glyph glyph-name="threefifths" unicode="&#x2157;" horiz-adv-x="540" 
d="M414 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5zM95 0l150 364l131 336h63l-150 -364
l-131 -336h-63zM112 345q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="fourfifths" unicode="&#x2158;" horiz-adv-x="540" 
d="M192 414v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM128 479v140h-7l-53 -140h60zM414 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3
q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5zM97 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="onesixth" unicode="&#x2159;" horiz-adv-x="495" 
d="M365 -5q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM366 62q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65zM152 350h-79v271l-52 -24v75l75 31
h56v-353zM53 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="fivesixths" unicode="&#x215a;" horiz-adv-x="540" 
d="M416 -5q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM417 62q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65zM109 345q-48 0 -78 25v72
q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5zM95 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="495" 
d="M364 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM364 300q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM364 154q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52zM152 350h-79v271l-52 -24v75l75 31h56v-353zM46 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="540" 
d="M421 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM421 300q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM421 154q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52zM95 0l150 364l131 336h63l-150 -364l-131 -336h-63zM112 345q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="540" 
d="M421 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM421 300q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM421 154q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52zM111 345q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5zM95 0l150 364l131 336h63
l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="540" 
d="M392 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM392 300q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM392 154q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52zM134 350h-78l91 278h-109v72h189v-53zM60 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="380" 
d="M356 292h-332v120h332v-120z" />
    <glyph glyph-name="Q.alt" horiz-adv-x="420" 
d="M295 -81l-48 76q-20 -5 -37 -5q-86 0 -136 87t-50 255v36q0 168 50 255t136 87t136 -87t50 -255v-36q0 -177 -58 -269l85 -144h-128zM210 102q20 0 33 19t21 71.5t8 139.5v36q0 87 -8 139.5t-21 71.5t-33 19t-33 -19t-21 -71.5t-8 -139.5v-36q0 -87 8 -139.5t21 -71.5
t33 -19z" />
    <glyph glyph-name="a.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147z" />
    <glyph glyph-name="aacute.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM153 580l86 147l93 -49l-99 -98h-80z" />
    <glyph glyph-name="abreve.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM200 574q-49 0 -81.5 30t-36.5 89h70q7 -47 48 -47
t48 47h70q-4 -59 -36.5 -89t-81.5 -30z" />
    <glyph glyph-name="acircumflex.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM78 580l78 139h88l78 -139h-77l-46 62l-46 -62h-75z
" />
    <glyph glyph-name="adieresis.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM218 583v108h106v-108h-106zM76 583v108h106v-108
h-106z" />
    <glyph glyph-name="agrave.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM167 580l-99 98l93 49l86 -147h-80z" />
    <glyph glyph-name="amacron.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM84 583v78h232v-78h-232z" />
    <glyph glyph-name="aogonek.alt" horiz-adv-x="384" 
d="M355 -79v-68q-20 -4 -41 -4q-97 0 -97 71q0 41 36 80h-20v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-16q-21 -28 -21 -51q0 -31 41 -28zM187 103q47 0 47 147v36q0 147 -47 147
q-44 0 -44 -147v-36q0 -147 44 -147z" />
    <glyph glyph-name="aring.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM200 574q-37 0 -62 25.5t-25 64.5t25.5 64.5
t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM200 619q17 0 28 13t11 32t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="aring.alt2" horiz-adv-x="350" 
d="M116 -9q-45 0 -73.5 43t-28.5 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-33 0 -89 -25v112q31 13 61 19q-15 19 -15 50q0 39 25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5q0 -36 -26 -62q21 -10 39 -27q48 -48 48 -143v-356h-118v57
q-31 -66 -87 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69zM134 588q0 -19 11 -32t28 -13t28 13t11 32t-11 32t-28 13t-28 -13t-11 -32z" />
    <glyph glyph-name="aringacute.alt" horiz-adv-x="384" 
d="M156 773l76 112l93 -44l-85 -68h-84zM139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM200 574
q-37 0 -62 25.5t-25 64.5t25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5t-25.5 -64.5t-61.5 -25.5zM200 619q17 0 28 13t11 32t-11 32t-28 13t-28 -13t-11 -32t11 -32t28 -13z" />
    <glyph glyph-name="aringacute.alt2" horiz-adv-x="350" 
d="M129 701l76 112l93 -44l-85 -68h-84zM116 -9q-45 0 -73.5 43t-28.5 115v17q0 87 31.5 128.5t87.5 41.5q36 0 71 -20v32q0 84 -75 84q-33 0 -89 -25v112q31 13 61 19q-15 19 -15 50q0 39 25.5 64.5t61.5 25.5q37 0 62 -25.5t25 -64.5q0 -36 -26 -62q21 -10 39 -27
q48 -48 48 -143v-356h-118v57q-31 -66 -87 -66zM164 90q16 0 28 13.5t12 37.5v92q-20 11 -37 11q-40 0 -40 -71v-14q0 -69 37 -69zM134 588q0 -19 11 -32t28 -13t28 13t11 32t-11 32t-28 13t-28 -13t-11 -32z" />
    <glyph glyph-name="atilde.alt" horiz-adv-x="384" 
d="M139 -10q-114 0 -114 254v48q0 254 114 254q24 0 44 -14t28.5 -26t21.5 -35v65h118v-536h-118v67q-12 -22 -21 -35.5t-29 -27.5t-44 -14zM187 103q47 0 47 147v36q0 147 -47 147q-44 0 -44 -147v-36q0 -147 44 -147zM266 579q-19 0 -63 20q-32 15 -42 15q-11 0 -16.5 -8
t-11.5 -31l-65 16q10 58 26.5 82t45.5 24q19 0 63 -20q32 -15 42 -15q11 0 16.5 8t11.5 31l65 -16q-10 -58 -26.5 -82t-45.5 -24z" />
    <glyph glyph-name="three.alt" horiz-adv-x="328" 
d="M120 -10q-58 0 -106 19v116q44 -19 82 -19q44 0 69.5 27t25.5 73v3q0 48 -31 75t-91 28v101q55 5 87.5 33.5t32.5 73.5v2q0 34 -20.5 53.5t-54.5 19.5q-39 0 -90 -27v113q58 29 114 29q74 0 121.5 -45t47.5 -129v-4q0 -59 -28 -100t-71 -61q103 -43 103 -168v-5
q0 -96 -54 -152t-137 -56z" />
    <glyph glyph-name="registered.alt" horiz-adv-x="522" 
d="M261 -10q-106 0 -171.5 93t-65.5 250v34q0 157 65.5 250t171.5 93t171.5 -93t65.5 -250v-34q0 -157 -65.5 -250t-171.5 -93zM261 26q92 0 145.5 83t53.5 223v36q0 140 -53.5 223t-145.5 83t-145.5 -83t-53.5 -223v-36q0 -140 53.5 -223t145.5 -83zM171 164v385h70
q56 0 86 -30q32 -32 32 -93v-5q0 -68 -45 -101l52 -156h-71l-44 134h-13v-134h-67zM238 359h4q50 0 50 62v4q0 61 -52 61h-2v-127z" />
    <glyph glyph-name="R_p" horiz-adv-x="710" 
d="M30 0v700h120q89 0 141 -55t52 -168v-9q0 -120 -73 -183l84 -285h-121l-72 247h-17v-247h-114zM386 -130v666h110v-67q10 22 18.5 35.5t27.5 27.5t42 14q107 0 107 -254v-48q0 -254 -107 -254q-23 0 -42 13.5t-27 26.5t-19 35v-195h-110zM535 98q38 0 38 147v46
q0 147 -38 147q-40 0 -40 -147v-46q0 -147 40 -147zM144 351h5q37 0 59 28.5t22 89.5v8q0 115 -83 115h-3v-241z" />
    <glyph glyph-name="onehalf.alt" horiz-adv-x="540" 
d="M218 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM330 0v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185zM105 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="onethird.alt" horiz-adv-x="540" 
d="M218 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM410 -5q-44 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31zM100 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="onequarter.alt" horiz-adv-x="540" 
d="M223 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM115 0l150 364l131 336h63l-150 -364l-131 -336h-63zM491 64v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM427 129v140h-7l-53 -140h60z" />
    <glyph glyph-name="onefifth.alt" horiz-adv-x="540" 
d="M218 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM409 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80
q0 -56 -33.5 -88.5t-82.5 -32.5zM105 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="onesixth.alt" horiz-adv-x="540" 
d="M218 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM426 -5q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM427 62q27 0 27 53t-30 53
q-15 0 -23 -10v-31q0 -65 26 -65zM110 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="oneeighth.alt" horiz-adv-x="540" 
d="M213 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66zM424 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM424 300q-29 0 -29 -43q0 -42 29 -42t29 42
q0 43 -29 43zM424 154q-32 0 -32 -52q0 -51 32 -51t32 51q0 52 -32 52zM105 0l150 364l131 336h63l-150 -364l-131 -336h-63z" />
    <glyph glyph-name="one.sup_alt" 
d="M224 461h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66z" />
    <glyph glyph-name="one.inf_alt" 
d="M224 -132h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66z" />
    <glyph glyph-name="zero.tab" horiz-adv-x="360" 
d="M180 -10q-86 0 -125.5 80.5t-39.5 260.5v37q0 181 39.5 261.5t125.5 80.5q85 0 125 -80.5t40 -260.5v-37q0 -180 -40 -261t-125 -81zM180 597q-18 0 -28 -16t-15.5 -67t-5.5 -146v-35q0 -96 5.5 -147t15.5 -67t28 -16q17 0 27 16t15.5 67t5.5 146v35q0 96 -5.5 147
t-15.5 67t-27 16z" />
    <glyph glyph-name="one.tab" horiz-adv-x="360" 
d="M352 0h-318v107h108v465l-105 -51v122l141 62h86v-598h88v-107z" />
    <glyph glyph-name="two.tab" horiz-adv-x="360" 
d="M336 0h-308v104l134 266q51 102 51 155q0 34 -18 50t-45 16q-48 0 -114 -56v120q58 55 137 55q70 0 115 -44t45 -120v-4q0 -44 -13.5 -89.5t-51.5 -122.5l-104 -208h172v-122z" />
    <glyph glyph-name="three.tab" horiz-adv-x="360" 
d="M136 -10q-72 0 -115 30v115q52 -29 96 -29q45 0 71 26.5t26 73.5v8q0 100 -133 108h-3l-1 81l121 177h-169v120h302v-109l-131 -182q136 -41 136 -186v-11q0 -102 -55.5 -162t-144.5 -60z" />
    <glyph glyph-name="three.tab_alt" horiz-adv-x="360" 
d="M136 -10q-68 0 -113 29v114q46 -27 95 -27q44 0 70.5 26.5t26.5 76.5q0 99 -129 102h-3v102h4q59 4 92 32t33 73q0 37 -21.5 57t-58.5 20q-48 0 -99 -35v111q52 39 126 39q76 0 124.5 -45.5t48.5 -130.5q0 -118 -103 -163q107 -45 107 -170q0 -97 -56.5 -154t-143.5 -57z
" />
    <glyph glyph-name="four.tab" horiz-adv-x="360" 
d="M196 0v130h-182l-2 95l148 477h145v-467h45v-105h-45v-130h-109zM196 235v268h-9l-80 -268h89z" />
    <glyph glyph-name="five.tab" horiz-adv-x="360" 
d="M139 -10q-64 0 -104 30v118q45 -31 94 -31q88 0 88 120v4q0 101 -67 101q-35 0 -60 -23l-61 40l20 351h263v-117h-171l-9 -150q26 11 52 11q68 0 111.5 -50t43.5 -159v-6q0 -112 -54.5 -175.5t-145.5 -63.5z" />
    <glyph glyph-name="six.tab" horiz-adv-x="360" 
d="M215 457q56 0 92 -49t36 -159v-18q0 -109 -43.5 -175t-115.5 -66q-57 0 -94 35q-67 68 -67 295v21q0 205 49.5 287t146.5 82q41 0 81 -18v-115q-38 22 -71 22q-45 0 -65.5 -40.5t-21.5 -130.5q31 29 73 29zM185 100q43 0 43 126v19q0 110 -47 110q-22 0 -40 -22v-84
q0 -80 10.5 -114.5t33.5 -34.5z" />
    <glyph glyph-name="seven.tab" horiz-adv-x="360" 
d="M207 576h-184v124h305v-88l-152 -612h-121z" />
    <glyph glyph-name="eight.tab" horiz-adv-x="360" 
d="M180 -10q-76 0 -120.5 54.5t-44.5 149.5v7q0 123 77 173q-66 44 -66 150v5q0 83 43 132t111 49t111 -49t43 -132v-5q0 -106 -66 -150q77 -50 77 -173v-7q0 -95 -44.5 -149.5t-120.5 -54.5zM180 412q46 0 46 99v6q0 96 -46 96t-46 -96v-6q0 -99 46 -99zM180 88
q52 0 52 112v7q0 110 -52 110t-52 -110v-7q0 -112 52 -112z" />
    <glyph glyph-name="nine.tab" horiz-adv-x="360" 
d="M145 235q-56 0 -92 51t-36 161v18q0 109 43.5 177t115.5 68q57 0 94 -35q67 -68 67 -295v-21q0 -205 -49.5 -287t-146.5 -82q-45 0 -91 21v115q47 -25 81 -25q44 0 64.5 38.5t22.5 124.5q-31 -29 -73 -29zM175 600q-43 0 -43 -130v-20q0 -114 47 -114q22 0 40 22v92
q0 80 -10.5 115t-33.5 35z" />
    <glyph glyph-name="period.tab" horiz-adv-x="180" 
d="M30 0v150h120v-150h-120z" />
    <glyph glyph-name="comma.tab" horiz-adv-x="180" 
d="M16 -157l-11 47q44 10 62 41t14 69h-51v150h120v-125q0 -165 -134 -182z" />
    <glyph glyph-name="colon.tab" horiz-adv-x="180" 
d="M30 386v150h120v-150h-120zM30 0v150h120v-150h-120z" />
    <glyph glyph-name="semicolon.tab" horiz-adv-x="180" 
d="M30 386v150h120v-150h-120zM16 -157l-11 47q44 10 62 41t14 69h-51v150h120v-125q0 -165 -134 -182z" />
    <glyph glyph-name="twodotleader.tab" horiz-adv-x="360" 
d="M210 0v146h120v-146h-120zM30 0v146h120v-146h-120z" />
    <glyph glyph-name="periodcentered.tab" horiz-adv-x="180" 
d="M30 235v149h120v-149h-120z" />
    <glyph glyph-name="slash.tab" horiz-adv-x="360" 
d="M353 769l-254 -868h-101l255 868h100z" />
    <glyph glyph-name="bar.tab" horiz-adv-x="180" 
d="M43 -99v868h94v-868h-94z" />
    <glyph glyph-name="underscore.tab" horiz-adv-x="360" 
d="M-2 -151v93h364v-93h-364z" />
    <glyph glyph-name="dollar.tab" horiz-adv-x="360" 
d="M253 774l-6 -73q32 -5 66 -20v-127q-58 34 -100 34q-27 0 -46 -16.5t-19 -43.5v-1q0 -22 13.5 -42.5t58.5 -70.5q72 -78 97 -123t25 -104v-2q0 -76 -41.5 -125.5t-107.5 -61.5l-8 -105h-80l8 106q-36 6 -78 28v126q66 -41 113 -41q33 0 52 18t19 50v1q0 22 -13.5 45.5
t-56.5 69.5q-71 74 -97 118t-26 105v2q0 73 39 121.5t102 59.5l6 72h80z" />
    <glyph glyph-name="sterling.tab" horiz-adv-x="360" 
d="M73 391v110q0 111 51 162q45 45 121 45q43 0 80 -13v-119q-36 12 -57 12q-32 0 -49 -17q-21 -21 -21 -70v-110h99v-111h-99v-168h147v-112h-322v82l50 24v174h-48v111h48z" />
    <glyph glyph-name="Euro.tab" horiz-adv-x="360" 
d="M13 388v90h54q17 117 69 174.5t125 57.5q39 0 76 -19v-125q-39 25 -65 25q-63 0 -80 -113h93v-90h-100v-24v-27v-21h100v-90h-94q17 -117 83 -117q26 0 65 24v-118q-40 -25 -86 -25q-71 0 -121 59t-66 177h-53v90h47v21v25v26h-47z" />
    <glyph glyph-name="yen.tab" horiz-adv-x="360" 
d="M40 189h82v61h-82v92h62l-99 358h122l56 -287l57 287h119l-100 -358h63v-92h-82v-61h82v-92h-82v-97h-116v97h-82v92z" />
    <glyph glyph-name="cent.tab" horiz-adv-x="360" 
d="M245 705v-77q39 -4 76 -26v-120q-49 32 -83 32q-87 0 -87 -145v-34q0 -144 87 -144q35 0 84 34v-114q-38 -29 -77 -35v-82h-80v88q-65 19 -100 84t-35 171v30q0 103 35.5 167.5t99.5 85.5v85h80z" />
    <glyph glyph-name="numbersign.tab" horiz-adv-x="360" 
d="M12 268h54l10 168h-46v108h53l10 156h76l-11 -156h65l11 156h76l-10 -156h49v-108h-56l-10 -168h48v-108h-55l-10 -160h-76l10 160h-65l-10 -160h-76l10 160h-47v108zM152 436l-10 -168h65l10 168h-65z" />
    <glyph glyph-name="paragraph.tab" horiz-adv-x="360" 
d="M193 0v224h-1q-72 9 -124.5 68.5t-52.5 169.5v2q0 121 62 178.5t160 57.5h80v-700h-124z" />
    <glyph glyph-name="section.tab" horiz-adv-x="360" 
d="M273 221q32 -43 32 -87v-2q0 -67 -39 -104.5t-103 -37.5q-50 0 -91 20v112q46 -26 80 -26q42 0 42 37v2q0 10 -5 19t-20 21t-22.5 17t-30.5 20q-35 23 -54 38.5t-35.5 44.5t-16.5 64v2q0 37 20.5 70t56.5 48q-32 43 -32 87v2q0 67 39 104.5t103 37.5q50 0 91 -20v-112
q-46 26 -80 26q-42 0 -42 -37v-2q0 -10 5 -19t20 -21t22.5 -17t30.5 -20q35 -23 54 -38.5t35.5 -44.5t16.5 -64v-2q0 -37 -20.5 -70t-56.5 -48zM139 430q-27 -21 -27 -53v-2q0 -40 88 -94q2 0 7 -3t9 -6l5 -2q27 21 27 53v2q0 40 -88 94q-2 0 -7 3t-9 6z" />
    <glyph glyph-name="degree.tab" horiz-adv-x="360" 
d="M180 302q-61 0 -98.5 54t-37.5 141v14q0 87 37.5 141t98.5 54t98.5 -54t37.5 -141v-14q0 -87 -37.5 -141t-98.5 -54zM180 388q51 0 51 109v14q0 109 -51 109t-51 -109v-14q0 -109 51 -109z" />
    <glyph glyph-name="percent.tab" horiz-adv-x="360" 
d="M108 706q99 0 99 -176v-36q0 -16 -1 -24l81 230h61l-134 -327q17 9 38 9q99 0 99 -176v-36q0 -176 -99 -176t-99 176v36v23l-80 -229h-61l134 326q-18 -8 -38 -8q-99 0 -99 176v36q0 176 99 176zM250 311q-15 0 -21 -22.5t-6 -82.5v-36q0 -60 6 -82.5t21 -22.5t21.5 23
t6.5 82v36q0 59 -6.5 82t-21.5 23zM110 635q-15 0 -21.5 -23t-6.5 -82v-36q0 -59 6.5 -82t21.5 -23t21 22.5t6 82.5v36q0 60 -6 82.5t-21 22.5z" />
    <glyph glyph-name="quotedbl.tab" horiz-adv-x="360" 
d="M217 381l2 319h122v-9l-79 -310h-45zM48 381l2 319h122v-9l-78 -310h-46z" />
    <glyph glyph-name="quotesingle.tab" horiz-adv-x="180" 
d="M44 381l2 319h122v-9l-78 -310h-46z" />
    <glyph glyph-name="plus.tab" horiz-adv-x="360" 
d="M342 298h-110v-118h-104v118h-110v108h110v118h104v-118h110v-108z" />
    <glyph glyph-name="minus.tab" horiz-adv-x="360" 
d="M340 293h-320v118h320v-118z" />
    <glyph glyph-name="divide.tab" horiz-adv-x="360" 
d="M126 458v113h108v-113h-108zM340 298h-320v108h320v-108zM126 133v113h108v-113h-108z" />
    <glyph glyph-name="equal.tab" horiz-adv-x="360" 
d="M339 389h-318v117h318v-117zM339 198h-318v117h318v-117z" />
    <glyph glyph-name="multiply.tab" horiz-adv-x="360" 
d="M267 171l-87 101l-88 -101l-82 69l100 112l-100 112l83 69l87 -101l88 101l82 -69l-100 -112l100 -112z" />
    <glyph glyph-name="less.tab" horiz-adv-x="360" 
d="M331 108l-304 193v102l304 193v-137l-179 -106l179 -106v-139z" />
    <glyph glyph-name="greater.tab" horiz-adv-x="360" 
d="M332 301l-304 -193v137l179 106l-179 106v139l304 -193v-102z" />
    <glyph glyph-name="plusminus.tab" horiz-adv-x="360" 
d="M342 303h-110v-119h-104v119h-110v107h110v119h104v-119h110v-107zM340 0h-320v104h320v-104z" />
    <glyph glyph-name="space.tab" horiz-adv-x="180" 
 />
    <glyph glyph-name="NUL" horiz-adv-x="0" 
 />
    <glyph glyph-name="hfjslug" horiz-adv-x="694" 
d="M644 -200h-208l-8 14h-163l-8 -14h-207v303q14 0 23 9.5t9 22.5t-9.5 22.5t-22.5 9.5v554h100l30 79h334l29 -79h101v-921zM400 563v-172h15v78h38v14h-38v66h43v14h-58zM184 563v-172h15v79h47v-79h15v172h-15v-79h-47v79h-15zM460 394q10 -4 21 -4q31 0 31 36v137h-16
v-137q0 -22 -16 -22q-8 0 -20 5v-15zM371 465l-13 3q-4 -18 -8 -30h-1l-16 31q23 21 23 42q0 12 -7 21.5t-20 9.5q-12 0 -20 -9.5t-8 -21.5q0 -13 14 -39q-23 -18 -23 -47q0 -16 8 -26.5t21 -10.5q19 0 29 24h1l12 -24l12 6l-17 32q6 14 13 39zM523 323v-46h8v12l8 8l15 -20
h10l-20 26l18 20h-10l-20 -24h-1v24h-8zM488 292l10 -15h9l-12 17q10 4 10 14q0 15 -17 15h-20v-46h8v15h12zM426 276q10 0 17 7t7 17q0 9 -7 16.5t-17 7.5t-17 -7.5t-7 -16.5q0 -10 7 -17t17 -7zM370 301l-13 22h-9l18 -29v-17h8v17l18 29h-9zM193 323v-46h34v7h-26v13h23
v7h-23v12h26v7h-34zM132 323v-46h8v32l25 -32h7v46h-8v-32l-25 32h-7zM241 323l14 -47h8l11 34h1l11 -34h8l14 47h-8l-10 -35h-1l-11 35h-7l-11 -35h-1l-10 35h-8zM327 479q-12 22 -12 32q0 19 14 19q13 0 13 -19q0 -15 -15 -32zM342 423q-8 -21 -20 -21q-16 0 -16 25
q0 19 16 35zM487 299h-11v17h11q10 0 10 -8q0 -9 -10 -9zM426 283q-16 0 -16 17q0 6 4.5 11.5t11.5 5.5t11.5 -5.5t4.5 -11.5q0 -17 -16 -17z" />
    <glyph glyph-name="i.dot" horiz-adv-x="186" 
d="M33 587v118h120v-118h-120zM34 0v536h118v-536h-118z" />
    <glyph glyph-name="zero.num" 
d="M120 345q-52 0 -76 42t-24 130v16q0 88 24 130t76 42t76 -42t24 -130v-16q0 -88 -24 -130t-76 -42zM120 636q-15 0 -20.5 -22t-5.5 -84v-10q0 -62 5.5 -84t20.5 -22t20.5 22t5.5 84v10q0 62 -5.5 84t-20.5 22z" />
    <glyph glyph-name="one.num" horiz-adv-x="195" 
d="M149 350h-79v271l-52 -24v75l75 31h56v-353z" />
    <glyph glyph-name="one.num_alt" 
d="M224 350h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66z" />
    <glyph glyph-name="two.num" 
d="M28 350v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185z" />
    <glyph glyph-name="three.num" 
d="M106 345q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="four.num" 
d="M201 414v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM137 479v140h-7l-53 -140h60z" />
    <glyph glyph-name="five.num" 
d="M105 345q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5z" />
    <glyph glyph-name="six.num" 
d="M122 345q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM123 412q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65z" />
    <glyph glyph-name="seven.num" 
d="M120 350h-78l91 278h-109v72h189v-53z" />
    <glyph glyph-name="eight.num" 
d="M120 345q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM120 650q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM120 504q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52z" />
    <glyph glyph-name="nine.num" 
d="M118 705q37 0 61 -24q39 -36 39 -140v-13q0 -183 -119 -183q-28 0 -61 18v73q29 -23 56 -23q45 0 48 65q-22 -13 -44 -13q-33 0 -56 26.5t-23 87.5q0 59 28.5 92.5t70.5 33.5zM120 529q15 0 23 10v32q0 67 -26 67q-27 0 -27 -55q0 -54 30 -54z" />
    <glyph glyph-name="zero.den" 
d="M120 -5q-52 0 -76 42t-24 130v16q0 88 24 130t76 42t76 -42t24 -130v-16q0 -88 -24 -130t-76 -42zM120 286q-15 0 -20.5 -22t-5.5 -84v-10q0 -62 5.5 -84t20.5 -22t20.5 22t5.5 84v10q0 62 -5.5 84t-20.5 22z" />
    <glyph glyph-name="one.den" horiz-adv-x="195" 
d="M149 0h-79v271l-52 -24v75l75 31h56v-353z" />
    <glyph glyph-name="one.den_alt" 
d="M224 0h-189v66h61v208l-59 -24v73l79 30h56v-287h52v-66z" />
    <glyph glyph-name="two.den" 
d="M28 0v64l75 115q32 55 34 71q0 15 -9 24t-25 9q-31 0 -68 -40v78q36 34 82 34q43 0 70.5 -25t27.5 -69q0 -45 -36 -98l-60 -91h94v-72h-185z" />
    <glyph glyph-name="three.den" 
d="M106 -5q-45 0 -77 25v72q30 -28 67 -28q47 0 47 43q0 40 -76 42h-2l-1 52l61 79h-94v70h186v-56l-74 -89q77 -20 77 -96q0 -52 -32 -83t-82 -31z" />
    <glyph glyph-name="four.den" 
d="M201 64v-64h-68v64h-117v66l84 222h101v-223h27v-65h-27zM137 129v140h-7l-53 -140h60z" />
    <glyph glyph-name="five.den" 
d="M105 -5q-48 0 -78 25v72q32 -28 68 -28q22 0 35.5 13.5t13.5 36.5q0 20 -11.5 30.5t-30.5 10.5q-15 0 -35 -8l-43 19l15 184h166v-69h-107q-1 -10 -3 -31t-2 -30q15 3 29 3q42 0 70.5 -27t28.5 -80q0 -56 -33.5 -88.5t-82.5 -32.5z" />
    <glyph glyph-name="six.den" 
d="M122 -5q-37 0 -61 24q-39 36 -39 140v13q0 183 119 183q33 0 57 -15l-4 -73q-27 20 -48 20q-45 0 -48 -68q22 13 44 13q33 0 56 -26.5t23 -86.5q0 -58 -28.5 -91t-70.5 -33zM123 62q27 0 27 53t-30 53q-15 0 -23 -10v-31q0 -65 26 -65z" />
    <glyph glyph-name="seven.den" 
d="M120 0h-78l91 278h-109v72h189v-53z" />
    <glyph glyph-name="eight.den" 
d="M120 -5q-48 0 -77 28t-29 74q0 61 45 88q-38 25 -38 76q0 43 27.5 68.5t71.5 25.5t71.5 -25.5t27.5 -68.5q0 -51 -38 -76q45 -27 45 -88q0 -46 -29 -74t-77 -28zM120 300q-29 0 -29 -43q0 -42 29 -42t29 42q0 43 -29 43zM120 154q-32 0 -32 -52q0 -51 32 -51t32 51
q0 52 -32 52z" />
    <glyph glyph-name="nine.den" 
d="M118 355q37 0 61 -24q39 -36 39 -140v-13q0 -183 -119 -183q-28 0 -61 18v73q29 -23 56 -23q45 0 48 65q-22 -13 -44 -13q-33 0 -56 26.5t-23 87.5q0 59 28.5 92.5t70.5 33.5zM120 179q15 0 23 10v32q0 67 -26 67q-27 0 -27 -55q0 -54 30 -54z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="500" 
d="M175 560l10 145h79v-2l-38 -143h-51z" />
    <glyph glyph-name="acute.cap" horiz-adv-x="500" 
d="M156 735l86 127l101 -50l-99 -77h-88z" />
    <glyph glyph-name="hungarumlaut.cap" horiz-adv-x="500" 
d="M218 735l44 64l-80 -64h-82l86 127l86 -48l32 48l91 -50l-95 -77h-82z" />
    <glyph glyph-name="grave.cap" horiz-adv-x="500" 
d="M332 735h-88l-99 77l101 50z" />
    <glyph glyph-name="circumflex.cap" horiz-adv-x="500" 
d="M128 735l74 119h96l74 -119h-82l-41 48l-41 -48h-80z" />
    <glyph glyph-name="caron.cap" horiz-adv-x="500" 
d="M372 854l-74 -119h-96l-74 119h82l41 -48l41 48h80z" />
    <glyph glyph-name="breve.cap" horiz-adv-x="500" 
d="M250 729q-51 0 -82.5 28.5t-32.5 82.5h70q6 -39 45 -39t45 39h70q-1 -54 -33 -82.5t-82 -28.5z" />
    <glyph glyph-name="tilde.cap" horiz-adv-x="500" 
d="M313 734q-22 0 -62 19q-34 16 -43 16q-10 0 -15 -8t-11 -31l-69 17q10 60 27 84.5t47 24.5q24 0 66 -21q32 -14 39 -14q10 0 15 8t11 31l69 -17q-10 -60 -27 -84.5t-47 -24.5z" />
    <glyph glyph-name="macron.cap" horiz-adv-x="500" 
d="M129 738v79h242v-79h-242z" />
    <glyph glyph-name="dieresis.cap" horiz-adv-x="500" 
d="M271 738v108h108v-108h-108zM121 738v108h108v-108h-108z" />
    <glyph glyph-name="dotaccent.cap" horiz-adv-x="500" 
d="M195 738v108h110v-108h-110z" />
    <glyph glyph-name="ring.cap" horiz-adv-x="500" 
d="M250 729q-36 0 -61.5 24t-25.5 60t25.5 60t61.5 24t61.5 -24t25.5 -60t-25.5 -60t-61.5 -24zM250 773q17 0 28 11.5t11 28.5t-11 28.5t-28 11.5t-28 -11.5t-11 -28.5t11 -28.5t28 -11.5z" />
    <hkern u1="&#x24;" u2="&#x39;" k="10" />
    <hkern u1="&#x24;" u2="&#x37;" k="10" />
    <hkern u1="&#x24;" u2="&#x35;" k="10" />
    <hkern u1="&#x24;" u2="&#x31;" k="10" />
    <hkern u1="&#x26;" u2="&#x37;" k="20" />
    <hkern u1="&#x26;" u2="&#x31;" k="30" />
    <hkern u1="&#x28;" u2="j" k="-30" />
    <hkern u1="&#x28;" u2="&#x34;" k="50" />
    <hkern u1="&#x2f;" u2="&#x34;" k="55" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="100" />
    <hkern u1="&#x30;" g2="three.alt" k="5" />
    <hkern u1="&#x30;" u2="&#x37;" k="10" />
    <hkern u1="&#x30;" u2="&#x33;" k="5" />
    <hkern u1="&#x30;" u2="&#x32;" k="10" />
    <hkern u1="&#x30;" u2="&#x31;" k="10" />
    <hkern u1="&#x32;" u2="&#x36;" k="5" />
    <hkern u1="&#x32;" u2="&#x34;" k="10" />
    <hkern u1="&#x33;" u2="&#x39;" k="5" />
    <hkern u1="&#x33;" u2="&#x37;" k="5" />
    <hkern u1="&#x33;" u2="&#x35;" k="5" />
    <hkern u1="&#x33;" u2="&#x32;" k="5" />
    <hkern u1="&#x34;" u2="&#x39;" k="5" />
    <hkern u1="&#x34;" u2="&#x37;" k="5" />
    <hkern u1="&#x34;" u2="&#x35;" k="5" />
    <hkern u1="&#x34;" u2="&#x31;" k="10" />
    <hkern u1="&#x35;" u2="&#x39;" k="5" />
    <hkern u1="&#x35;" u2="&#x37;" k="10" />
    <hkern u1="&#x35;" u2="&#x35;" k="5" />
    <hkern u1="&#x36;" u2="&#x39;" k="5" />
    <hkern u1="&#x36;" u2="&#x37;" k="10" />
    <hkern u1="&#x36;" u2="&#x31;" k="10" />
    <hkern u1="&#x37;" u2="&#xa2;" k="20" />
    <hkern u1="&#x37;" u2="&#x40;" k="30" />
    <hkern u1="&#x37;" u2="&#x39;" k="5" />
    <hkern u1="&#x37;" u2="&#x38;" k="5" />
    <hkern u1="&#x37;" u2="&#x36;" k="10" />
    <hkern u1="&#x37;" u2="&#x34;" k="35" />
    <hkern u1="&#x37;" u2="&#x32;" k="5" />
    <hkern u1="&#x37;" u2="&#x30;" k="5" />
    <hkern u1="&#x37;" u2="&#x2f;" k="30" />
    <hkern u1="&#x37;" u2="&#x26;" k="35" />
    <hkern u1="&#x38;" u2="&#x37;" k="5" />
    <hkern u1="&#x38;" u2="&#x32;" k="5" />
    <hkern u1="&#x39;" g2="three.alt" k="5" />
    <hkern u1="&#x39;" u2="&#x37;" k="10" />
    <hkern u1="&#x39;" u2="&#x33;" k="10" />
    <hkern u1="&#x39;" u2="&#x32;" k="10" />
    <hkern u1="&#x40;" g2="three.alt" k="20" />
    <hkern u1="&#x40;" u2="&#x37;" k="25" />
    <hkern u1="&#x40;" u2="&#x33;" k="20" />
    <hkern u1="&#x40;" u2="&#x32;" k="20" />
    <hkern u1="F" u2="&#x2f;" k="50" />
    <hkern u1="F" u2="&#x26;" k="30" />
    <hkern u1="Q" u2="]" k="20" />
    <hkern u1="Q" u2="&#x3f;" k="10" />
    <hkern u1="Q" u2="&#x2f;" k="20" />
    <hkern u1="Q" u2="&#x29;" k="20" />
    <hkern u1="V" u2="&#x40;" k="15" />
    <hkern u1="V" u2="&#x3f;" k="-10" />
    <hkern u1="V" u2="&#x2f;" k="50" />
    <hkern u1="V" u2="&#x26;" k="20" />
    <hkern u1="[" u2="j" k="-30" />
    <hkern u1="[" u2="&#x34;" k="50" />
    <hkern u1="g" u2="&#x2f;" k="-5" />
    <hkern u1="j" u2="&#x2f;" k="-5" />
    <hkern u1="q" u2="&#x2f;" k="-10" />
    <hkern u1="&#xa3;" u2="&#x39;" k="10" />
    <hkern u1="&#xa3;" u2="&#x37;" k="10" />
    <hkern u1="&#xa3;" u2="&#x34;" k="10" />
    <hkern u1="&#xa3;" u2="&#x31;" k="10" />
    <hkern u1="&#xa5;" u2="&#x34;" k="5" />
    <hkern u1="&#xdf;" u2="]" k="10" />
    <hkern u1="&#xdf;" u2="&#x3f;" k="25" />
    <hkern u1="&#xdf;" u2="&#x29;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2f;" k="-5" />
    <hkern u1="&#x121;" u2="&#x2f;" k="-5" />
    <hkern u1="&#x123;" u2="&#x2f;" k="-5" />
    <hkern u1="&#x192;" u2="&#x34;" k="30" />
    <hkern g1="Q.alt" u2="]" k="20" />
    <hkern g1="Q.alt" u2="&#x3f;" k="10" />
    <hkern g1="Q.alt" u2="&#x2f;" k="20" />
    <hkern g1="Q.alt" u2="&#x29;" k="20" />
    <hkern g1="three.alt" u2="&#x39;" k="5" />
    <hkern g1="three.alt" u2="&#x37;" k="5" />
    <hkern g1="three.alt" u2="&#x35;" k="5" />
    <hkern g1="three.alt" u2="&#x32;" k="5" />
    <hkern g1="slash.tab" u2="&#x2f;" k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="asterisk"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="bracketright"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="parenright"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="question"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="v"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="x"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="T,Tcommaaccent,Tcaron"
	k="30" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="V"
	k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="X"
	k="15" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteright,quotedblright"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="quoteleft,quotedblleft"
	k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="registered,servicemark,trademark"
	k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="B"
	g2="bracketright"
	k="10" />
    <hkern g1="B"
	g2="parenright"
	k="10" />
    <hkern g1="B"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="B"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="-10" />
    <hkern g1="B"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="B"
	g2="V"
	k="5" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="B"
	g2="X"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="bracketright"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="parenright"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="question"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="slash"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="J"
	k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="v"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="x"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="V"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
	g2="X"
	k="20" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="fiveeighths"
	k="16" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="fivesixths"
	k="14" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="fourfifths"
	k="1" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="oneeighth"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="oneeighth.alt"
	k="-1" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onefifth"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onefifth.alt"
	k="4" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onehalf"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onehalf.alt"
	k="4" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onequarter"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onequarter.alt"
	k="9" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onesixth"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onesixth.alt"
	k="4" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onethird"
	k="13" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="onethird.alt"
	k="4" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="seveneighths"
	k="24" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="threeeighths"
	k="16" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="threefifths"
	k="16" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="threequarters"
	k="11" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="twofifths"
	k="6" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="twothirds"
	k="6" />
    <hkern g1="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	g2="zero.num,one.num,one.num_alt,two.num,three.num,four.num,five.num,six.num,seven.num,eight.num,nine.num"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="ampersand"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="AE,AEacute"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="hyphen,endash,emdash"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="30" />
    <hkern g1="K,Kcommaaccent"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="t,tcommaaccent,tcaron"
	k="15" />
    <hkern g1="K,Kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="x"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="V"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="X"
	k="5" />
    <hkern g1="K,Kcommaaccent"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="asterisk"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="bracketright"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="parenright"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="question"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="v"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="T,Tcommaaccent,Tcaron"
	k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="V"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="X"
	k="5" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteright,quotedblright"
	k="50" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="quoteleft,quotedblleft"
	k="40" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="registered,servicemark,trademark"
	k="30" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="10" />
    <hkern g1="P"
	g2="bracketright"
	k="10" />
    <hkern g1="P"
	g2="parenright"
	k="10" />
    <hkern g1="P"
	g2="slash"
	k="50" />
    <hkern g1="P"
	g2="ampersand"
	k="25" />
    <hkern g1="P"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="35" />
    <hkern g1="P"
	g2="J"
	k="60" />
    <hkern g1="P"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="P"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="120" />
    <hkern g1="P"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="5" />
    <hkern g1="P"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="P"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="P"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="20" />
    <hkern g1="P"
	g2="X"
	k="15" />
    <hkern g1="colon,semicolon,colon.tab,semicolon.tab"
	g2="one"
	k="10" />
    <hkern g1="colon,semicolon,colon.tab,semicolon.tab"
	g2="seven"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="one"
	k="45" />
    <hkern g1="hyphen,endash,emdash"
	g2="seven"
	k="40" />
    <hkern g1="hyphen,endash,emdash"
	g2="three"
	k="30" />
    <hkern g1="hyphen,endash,emdash"
	g2="three.alt"
	k="30" />
    <hkern g1="hyphen,endash,emdash"
	g2="two"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="AE,AEacute"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="J"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="5" />
    <hkern g1="hyphen,endash,emdash"
	g2="v"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="hyphen,endash,emdash"
	g2="x"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zacute,zdotaccent,zcaron"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="T,Tcommaaccent,Tcaron"
	k="45" />
    <hkern g1="hyphen,endash,emdash"
	g2="V"
	k="25" />
    <hkern g1="hyphen,endash,emdash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="35" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="one"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="four"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="nine"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="six"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="zero"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="5" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="v"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="T,Tcommaaccent,Tcaron"
	k="35" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="V"
	k="40" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="65" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="quoteright,quotedblright"
	k="50" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="quoteleft,quotedblleft"
	k="50" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis,period.tab,comma.tab,twodotleader.tab"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="questiondown"
	k="50" />
    <hkern g1="quoteleft,quotedblleft"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="quoteleft,quotedblleft"
	g2="J"
	k="70" />
    <hkern g1="quoteleft,quotedblleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="quoteleft,quotedblleft"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="quoteleft,quotedblleft"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="25" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="T,Tcommaaccent,Tcaron"
	k="5" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="ampersand"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="hyphen,endash,emdash"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="X"
	k="15" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="5" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="bracketright"
	k="20" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="parenright"
	k="20" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="question"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="slash"
	k="20" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="J"
	k="25" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="x"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="V"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="X"
	k="20" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="bracketright"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="parenright"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="-10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="v"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="x"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="V"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="X"
	k="5" />
    <hkern g1="S,Sacute,Scedilla,Scaron,Scommaaccent"
	g2="quoteright,quotedblright"
	k="15" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="slash"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="ampersand"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="at"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="J"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="v"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="x"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="30" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="V"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="X"
	k="5" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotright,guilsinglright"
	k="35" />
    <hkern g1="T,Tcommaaccent,Tcaron"
	g2="guillemotleft,guilsinglleft"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="question"
	k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash"
	k="40" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ampersand"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="at"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="30" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="v"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="x"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="X"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="X"
	g2="ampersand"
	k="5" />
    <hkern g1="X"
	g2="AE,AEacute"
	k="15" />
    <hkern g1="X"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="15" />
    <hkern g1="X"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="X"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="X"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="X"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="X"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="X"
	g2="t,tcommaaccent,tcaron"
	k="15" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="15" />
    <hkern g1="X"
	g2="v"
	k="25" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="X"
	g2="x"
	k="10" />
    <hkern g1="X"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="X"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="X"
	g2="T,Tcommaaccent,Tcaron"
	k="5" />
    <hkern g1="X"
	g2="V"
	k="10" />
    <hkern g1="X"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="X"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="X"
	g2="X"
	k="5" />
    <hkern g1="X"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="X"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="question"
	k="-5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="slash"
	k="75" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ampersand"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="at"
	k="5" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="J"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,endash,emdash"
	k="50" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="65" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="40" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="v"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="x"
	k="30" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="z,zacute,zdotaccent,zcaron"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="V"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="X"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotright,guilsinglright"
	k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="question"
	k="20" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="v"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="x"
	k="5" />
    <hkern g1="h,m,n,ntilde,hbar,nacute,ncommaaccent,ncaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="bracketright"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="parenright"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="question"
	k="30" />
    <hkern g1="b,p,thorn"
	g2="exclam"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="5" />
    <hkern g1="b,p,thorn"
	g2="v"
	k="15" />
    <hkern g1="b,p,thorn"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="b,p,thorn"
	g2="x"
	k="15" />
    <hkern g1="b,p,thorn"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="g,q,u,ugrave,uacute,ucircumflex,udieresis,gbreve,gdotaccent,gcommaaccent,umacron,ubreve,uring,uhungarumlaut,uogonek,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	g2="question"
	k="20" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="bracketright"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="parenright"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="question"
	k="30" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="exclam"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="v"
	k="20" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="x"
	k="15" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute,schwa"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2"
	g2="question"
	k="30" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2"
	g2="v"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute,aring.alt2,aringacute.alt2"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="question"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="v"
	k="10" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="c,ccedilla,cacute,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="question"
	k="30" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="exclam"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="v"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="x"
	k="5" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="f,f_f"
	g2="bracketright"
	k="-10" />
    <hkern g1="f,f_f"
	g2="parenright"
	k="-15" />
    <hkern g1="f,f_f"
	g2="slash"
	k="10" />
    <hkern g1="f,f_f"
	g2="ampersand"
	k="15" />
    <hkern g1="f,f_f"
	g2="at"
	k="10" />
    <hkern g1="f,f_f"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="f,f_f"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="35" />
    <hkern g1="f,f_f"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="f,f_f"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="f,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="5" />
    <hkern g1="f,f_f"
	g2="v"
	k="5" />
    <hkern g1="f,f_f"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="f,f_f"
	g2="x"
	k="5" />
    <hkern g1="f,f_f"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="f,f_f"
	g2="registered,servicemark,trademark"
	k="-20" />
    <hkern g1="k,kcommaaccent"
	g2="ampersand"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="k,kcommaaccent"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="5" />
    <hkern g1="k,kcommaaccent"
	g2="v"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="k,kcommaaccent"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="slash"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="ampersand"
	k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="at"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="exclam"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="35" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="bracketright"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="parenright"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="question"
	k="25" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="v"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="x"
	k="5" />
    <hkern g1="s,sacute,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="v"
	g2="bracketright"
	k="20" />
    <hkern g1="v"
	g2="parenright"
	k="20" />
    <hkern g1="v"
	g2="question"
	k="20" />
    <hkern g1="v"
	g2="slash"
	k="30" />
    <hkern g1="v"
	g2="ampersand"
	k="20" />
    <hkern g1="v"
	g2="at"
	k="20" />
    <hkern g1="v"
	g2="exclam"
	k="10" />
    <hkern g1="v"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="15" />
    <hkern g1="v"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="v"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="40" />
    <hkern g1="v"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="v"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="v"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="v"
	g2="v"
	k="25" />
    <hkern g1="v"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="v"
	g2="x"
	k="10" />
    <hkern g1="v"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="v"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="v"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="bracketright"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="parenright"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="question"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="slash"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ampersand"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="at"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="exclam"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="40" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="v"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="x"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="x"
	g2="bracketright"
	k="10" />
    <hkern g1="x"
	g2="parenright"
	k="10" />
    <hkern g1="x"
	g2="question"
	k="10" />
    <hkern g1="x"
	g2="ampersand"
	k="5" />
    <hkern g1="x"
	g2="at"
	k="10" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="x"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="x"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="15" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="5" />
    <hkern g1="x"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="x"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="x"
	g2="v"
	k="10" />
    <hkern g1="x"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="bracketright"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="parenright"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="question"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="slash"
	k="30" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ampersand"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="at"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="exclam"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="40" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="v"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="x"
	k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="guillemotleft,guilsinglleft"
	k="10" />
    <hkern g1="F"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="25" />
    <hkern g1="F"
	g2="J"
	k="55" />
    <hkern g1="F"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="20" />
    <hkern g1="F"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="F"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="50" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="F"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="F"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="F"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="15" />
    <hkern g1="F"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="F"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="F"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="F"
	g2="v"
	k="15" />
    <hkern g1="F"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="F"
	g2="x"
	k="15" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="F"
	g2="z,zacute,zdotaccent,zcaron"
	k="15" />
    <hkern g1="Q"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="Q"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="Q"
	g2="J"
	k="25" />
    <hkern g1="Q"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="Q"
	g2="x"
	k="10" />
    <hkern g1="Q"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="Q"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="Q"
	g2="V"
	k="10" />
    <hkern g1="Q"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Q"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Q"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Q.alt"
	g2="AE,AEacute"
	k="10" />
    <hkern g1="Q.alt"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="10" />
    <hkern g1="Q.alt"
	g2="J"
	k="25" />
    <hkern g1="Q.alt"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="Q.alt"
	g2="x"
	k="10" />
    <hkern g1="Q.alt"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="5" />
    <hkern g1="Q.alt"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="Q.alt"
	g2="V"
	k="10" />
    <hkern g1="Q.alt"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="Q.alt"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="Q.alt"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="Q.alt"
	g2="X"
	k="20" />
    <hkern g1="V"
	g2="AE,AEacute"
	k="20" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="V"
	g2="J"
	k="30" />
    <hkern g1="V"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="V"
	g2="hyphen,endash,emdash"
	k="25" />
    <hkern g1="V"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="40" />
    <hkern g1="V"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="V"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="20" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="V"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="25" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="V"
	g2="v"
	k="5" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="V"
	g2="x"
	k="5" />
    <hkern g1="V"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="V"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="V"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="V"
	g2="V"
	k="10" />
    <hkern g1="V"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="10" />
    <hkern g1="V"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="V"
	g2="X"
	k="10" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="30" />
    <hkern g1="V"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="ampersand"
	g2="v"
	k="20" />
    <hkern g1="ampersand"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="ampersand"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="ampersand"
	g2="T,Tcommaaccent,Tcaron"
	k="25" />
    <hkern g1="ampersand"
	g2="V"
	k="10" />
    <hkern g1="ampersand"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="ampersand"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="at"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="at"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="25" />
    <hkern g1="at"
	g2="J"
	k="30" />
    <hkern g1="at"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="10" />
    <hkern g1="at"
	g2="T,Tcommaaccent,Tcaron"
	k="20" />
    <hkern g1="at"
	g2="V"
	k="5" />
    <hkern g1="at"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="at"
	g2="X"
	k="20" />
    <hkern g1="bracketleft"
	g2="AE,AEacute"
	k="20" />
    <hkern g1="bracketleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="bracketleft"
	g2="J"
	k="30" />
    <hkern g1="bracketleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="bracketleft"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="bracketleft"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="bracketleft"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="bracketleft"
	g2="v"
	k="20" />
    <hkern g1="bracketleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="bracketleft"
	g2="x"
	k="10" />
    <hkern g1="bracketleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="bracketleft"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="10" />
    <hkern g1="fiveeighths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="9" />
    <hkern g1="fivesixths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="16" />
    <hkern g1="four"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="four"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="fourfifths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="1" />
    <hkern g1="germandbls"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="5" />
    <hkern g1="germandbls"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="germandbls"
	g2="v"
	k="10" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="germandbls"
	g2="x"
	k="5" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="nine"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="10" />
    <hkern g1="oneeighth"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="21" />
    <hkern g1="oneeighth.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="6" />
    <hkern g1="onefifth"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="13" />
    <hkern g1="onefifth.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="6" />
    <hkern g1="onehalf"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="17" />
    <hkern g1="onehalf.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="8" />
    <hkern g1="onequarter"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="22" />
    <hkern g1="onequarter.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="20" />
    <hkern g1="onesixth"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="22" />
    <hkern g1="onesixth.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="6" />
    <hkern g1="onethird"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="3" />
    <hkern g1="onethird.alt"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="6" />
    <hkern g1="parenleft"
	g2="AE,AEacute"
	k="20" />
    <hkern g1="parenleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="20" />
    <hkern g1="parenleft"
	g2="J"
	k="30" />
    <hkern g1="parenleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="parenleft"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="10" />
    <hkern g1="parenleft"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="parenleft"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="parenleft"
	g2="v"
	k="20" />
    <hkern g1="parenleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="parenleft"
	g2="x"
	k="10" />
    <hkern g1="parenleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="parenleft"
	g2="S,Sacute,Scedilla,Scaron,Scommaaccent"
	k="10" />
    <hkern g1="questiondown"
	g2="AE,AEacute"
	k="-10" />
    <hkern g1="questiondown"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="-10" />
    <hkern g1="questiondown"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="questiondown"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="questiondown"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="questiondown"
	g2="v"
	k="20" />
    <hkern g1="questiondown"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="questiondown"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="questiondown"
	g2="T,Tcommaaccent,Tcaron"
	k="15" />
    <hkern g1="questiondown"
	g2="V"
	k="20" />
    <hkern g1="questiondown"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="20" />
    <hkern g1="seven"
	g2="colon,semicolon,colon.tab,semicolon.tab"
	k="10" />
    <hkern g1="seven"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="seven"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="55" />
    <hkern g1="seveneighths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="38" />
    <hkern g1="slash"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="slash"
	g2="J"
	k="65" />
    <hkern g1="slash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="slash"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="35" />
    <hkern g1="slash"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="30" />
    <hkern g1="slash"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="slash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="25" />
    <hkern g1="slash"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="slash"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="30" />
    <hkern g1="slash"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="slash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="slash"
	g2="v"
	k="20" />
    <hkern g1="slash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="slash"
	g2="x"
	k="20" />
    <hkern g1="slash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="slash.tab"
	g2="AE,AEacute"
	k="35" />
    <hkern g1="slash.tab"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="50" />
    <hkern g1="slash.tab"
	g2="J"
	k="65" />
    <hkern g1="slash.tab"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="slash.tab"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="35" />
    <hkern g1="slash.tab"
	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,racute,rcommaaccent,rcaron"
	k="30" />
    <hkern g1="slash.tab"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="40" />
    <hkern g1="slash.tab"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="25" />
    <hkern g1="slash.tab"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="slash.tab"
	g2="s,sacute,scedilla,scaron,scommaaccent"
	k="30" />
    <hkern g1="slash.tab"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="slash.tab"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="30" />
    <hkern g1="slash.tab"
	g2="v"
	k="20" />
    <hkern g1="slash.tab"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="20" />
    <hkern g1="slash.tab"
	g2="x"
	k="20" />
    <hkern g1="slash.tab"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="20" />
    <hkern g1="threeeighths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="9" />
    <hkern g1="threefifths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="1" />
    <hkern g1="threequarters"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="16" />
    <hkern g1="two"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="twofifths"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="1" />
    <hkern g1="twothirds"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="-4" />
    <hkern g1="zero"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="hyphen,endash,emdash"
	k="45" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="25" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="v"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Cdotaccent,Ccaron"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="v"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	g2="zero.den,one.den,one.den_alt,two.den,three.den,four.den,five.den,six.den,seven.den,eight.den,nine.den"
	k="10" />
    <hkern g1="zero.inf,one.inf,two.inf,three.inf,four.inf,five.inf,six.inf,seven.inf,eight.inf,nine.inf,one.inf_alt"
	g2="zero.inf,one.inf,two.inf,three.inf,four.inf,five.inf,six.inf,seven.inf,eight.inf,nine.inf,one.inf_alt"
	k="10" />
    <hkern g1="two.sup,three.sup,one.sup,zero.sup,four.sup,five.sup,six.sup,seven.sup,eight.sup,nine.sup,one.sup_alt"
	g2="two.sup,three.sup,one.sup,zero.sup,four.sup,five.sup,six.sup,seven.sup,eight.sup,nine.sup,one.sup_alt"
	k="10" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="v"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="T,Tcommaaccent,Tcaron"
	k="10" />
    <hkern g1="G,Gbreve,Gdotaccent,Gcommaaccent"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="J"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="J"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="J"
	g2="J"
	k="5" />
    <hkern g1="J"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="J"
	g2="X"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="v"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron"
	k="35" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="V"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="35" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="v"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,Tcommaaccent,Tcaron"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="30" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="25" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="50" />
    <hkern g1="quoteright,quotedblright"
	g2="AE,AEacute"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="J"
	k="80" />
    <hkern g1="quoteright,quotedblright"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="quoteright,quotedblright"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="45" />
    <hkern g1="quoteright,quotedblright"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,aring.alt2,aringacute.alt2"
	k="30" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="J"
	k="15" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="comma,period,ellipsis,period.tab,comma.tab,twodotleader.tab"
	k="5" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="X"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Cdotaccent,Ccaron,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute,Q.alt"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="15" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="f,f_f,fi,fl,f_f_i,f_f_l"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="t,tcommaaccent,tcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="v"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="5" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="hyphen,endash,emdash"
	k="15" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="d,g,q,dcaron,dcroat,gbreve,gdotaccent,gcommaaccent,a.alt,aacute.alt,abreve.alt,acircumflex.alt,adieresis.alt,agrave.alt,amacron.alt,aogonek.alt,aring.alt,atilde.alt"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="t,tcommaaccent,tcaron"
	k="5" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="v"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="t,tcommaaccent,tcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,endash,emdash"
	k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute"
	k="10" />
  </font>
</defs></svg>
