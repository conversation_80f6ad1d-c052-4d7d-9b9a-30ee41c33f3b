import moment from "moment";

export var Utility = {
  leaderboard_assets: {
    expired: moment(),
    leaderboard_logo_url: null,
    challenge_logo_url: null,
    lionheart_logo_url: null,
  },
  isLeaderboardAssetsExpired: function (){
    if (Utility.leaderboard_assets.expired) {
      return Utility.leaderboard_assets.expired.isBefore(moment());
    } else {
      return false;
    }
  },
  callAPI: function (url, params, callback, token?, removeContentType?) {
    var xhr = new XMLHttpRequest();
    var apiUrl = url
    if (params) {
      var queryString = Object.keys(params).map(function (key) {
        return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
      }).join('&');
      apiUrl += '?' + queryString;
    }

    xhr.open('GET', apiUrl, true);

    if (!removeContentType) {
      xhr.setRequestHeader('Content-Type', 'application/json');
    }

    if (token) {
      xhr.setRequestHeader('Authorization', 'Bearer ' + token);
    }

    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
          callback(null, JSON.parse(xhr.responseText));
        } else {
          try {
            callback(JSON.parse(xhr.responseText));
          } catch (e) {
            callback('Something went wrong. Please try again later.');
          }
        }
      }
    };

    xhr.send();
  },
  callToStudioAPI: function (url, params, callback, token) {
    var xhr = new XMLHttpRequest();
    var apiUrl = url
    if (params) {
      var queryString = Object.keys(params).map(function (key) {
        return encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
      }).join('&');
      apiUrl += '?' + queryString;
    }

    xhr.open('GET', apiUrl, true);

    xhr.setRequestHeader('Content-Type', 'application/json');

    if (token) {
      xhr.setRequestHeader('Access-Token', token);
    }

    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
          callback(null, JSON.parse(xhr.responseText));
        } else {
          callback(JSON.parse(xhr.responseText));
        }
      }
    };

    xhr.send();
  },
  getParamFromUrl: function (param_slug) {
    var queryString = window.location.search.substring(1);
    var params = {};
    if (queryString) {
      var paramPairs = queryString.split('&');
      for (var i = 0; i < paramPairs.length; i++) {
        var param = paramPairs[i].split('=');
        var paramName = decodeURIComponent(param[0]);
        params[paramName] = decodeURIComponent(param[1]);
      }
    }
    return params[param_slug] || null;
  },
  getNumber: function (number) {
    if (number) {
      if (Math.floor(number) === number) {
        return parseInt(number);
      } else {
        return parseFloat(number).toFixed(1);
      }
    } else {
      return '-';
    }
  },
  getCurrentDate: function () {
    return moment().format('YYYY-MM-DD');
  },
  getDateByFormat: function (dateString) {
    return moment(dateString, "YYYY-MM-DD").format('MMM Do');
  },
  showLoading: function (flag) {
    if (flag) {
      document.getElementById("loading").style.display = "block";
    } else {
      document.getElementById("loading").style.display = "none";
    }
  },
  showLeaderboardTable: function (flag) {
    if (flag) {
      document.getElementById("leaderboard_table").style.display = "block";
    } else {
      document.getElementById("leaderboard_table").style.display = "none";
    }
    this.showLeaderboardAds(!flag);
  },
  showLionheartAds: function (flag) {
    if (flag) {
      document.getElementById("lionheart_ads").style.display = "block";
    } else {
      document.getElementById("lionheart_ads").style.display = "none";
    }
  },
  showLeaderboardAds: function (flag) {
    if (flag) {
      document.getElementById("leaderboard_ads").style.display = "block";
    } else {
      document.getElementById("leaderboard_ads").style.display = "none";
    }
  },
  getCurrentWeek: function (dateString) {
    var today = null;

    if (dateString) {
      today = moment(dateString, "YYYY-MM-DD");
    } else {
      today = moment();
    }

    var startOfWeek = today.clone().startOf('isoWeek').format('YYYY-MM-DD');
    var endOfWeek = today.clone().endOf('isoWeek').format('YYYY-MM-DD');

    console.log('startOfWeek: ' + startOfWeek);

    return {
      startOfWeek: startOfWeek,
      endOfWeek: endOfWeek
    };
  },
  getDaysOfWeek: function (dateString) {
    var daysOfWeek = [];
    var startDate = moment(dateString, "YYYY-MM-DD");
    daysOfWeek.push(startDate.format('YYYY-MM-DD'));
    for (var idx = 0; idx < 6; idx++) {
      daysOfWeek.push(startDate.add(1, 'days').format('YYYY-MM-DD'));
    }
    return daysOfWeek;
  },
  getDisplayName: function (user) {

    var display_name = 'F45 Member';

    if (user.display_name) {

      return user.display_name;

      // if (user.is_anonymized) {
      //     display_name = user.display_name;
      // } else {
      //     var names = user.display_name.split(' ');
      //     display_name = names[0];
      //     if (names.length > 1) {
      //         display_name += ' ' + names[1][0];
      //     }
      // }

    }

    return display_name;
  },
  getAvatarByName: function (user) {
    var avatarString = 'FM';

    if (user.display_name) {
      var names = user.display_name.split(' ');
      avatarString = names[0][0];
      if (names.length > 1) {
        avatarString += names[1][0];
      }
    }

    return avatarString.toUpperCase();
  },
  addEmptyUser: function (daysOfWeek, today){
    var emptyUserHtml = '';
    for (var idx = 0; idx < daysOfWeek.length; idx++) {
      if (daysOfWeek[idx] === today) {
        emptyUserHtml += '<td class="today"></td>';
      } else {
        emptyUserHtml += '<td></td>';
      }
    }
    return emptyUserHtml;
  },
  addRows: function (rows, daysOfWeek, today) {
    if (rows <= 0) return '';
    var html = '';
    for (var idx = 0; idx < rows; idx++) {
      html += '<tr>' +
        '<td></td>' +
        '<td class="col-avatar">' +
        '<div class="d-flex align-items-center justify-content-center">' +
        '<div class="avatar">' +
        '<span></span>' +
        '</div>' +
        '</div>' +
        '</td>' +
        '<td class="text-start user-name"></td>' + this.addEmptyUser(daysOfWeek, today) +
        '<td></td>' +
        '</tr>';
    }
    return html;
  },
  addPaddingRow: function (daysOfWeek, today){
    var html = '<tr> <td></td> <td></td> <td></td>';
    for (var idx = 0; idx < daysOfWeek.length; idx++) {
      var is_today = (daysOfWeek[idx] === today) ? 'class="today"' : '';
      if (idx === 2){
        html += '<td ' + is_today + '><div class="td text-center ps-0">• • •</div></td>';
      }else {
        html += '<td ' + is_today +'></td>';
      }
    }

    html +='<td></td>';
    html +='</tr>';

    return html;
  },
  addEmptyStudio: function (daysOfWeek, today){
    var html = '';
    for (var idx = 0; idx < daysOfWeek.length; idx++) {
      var is_today = (daysOfWeek[idx] === today) ? 'class="today"' : '';
      html +='<td ' + is_today + '><div class="td"></div></td>'
    }
    return html;
  },
  addStudioRows: function (rows, daysOfWeek, today){
    if (rows <= 0) return '';
    var html = '';
    for (var idx = 0; idx < rows; idx++) {
      html += '<tr>' +
        '       <td><div class="td"></div></td>' +
        '       <td><div class="td"><div class="mx-auto"></div></div></td>' +
        '       <td class="text-start user-name"><div class="td"></div></td>' +
        this.addEmptyStudio(daysOfWeek, today) +
        '       <td></td>' +
        '    </tr>';
    }
    return html;
  },
  processWorkoutData: function (workoutData){
    if (!workoutData) return null;
    var workouts = {};
    for(var idx = 0; idx < workoutData.length; idx++){
      workouts[workoutData[idx].date] = (workoutData[idx].program.media_logo_small.value).replace('http://', 'https://');
    }
    return workouts;
  },
  addClassToHtml: function (class_name){
    document.getElementsByTagName("html")[0].classList.remove(class_name);
    document.getElementsByTagName("html")[0].classList.add(class_name);
  },
  removeClassToHtml: function (class_name){
    document.getElementsByTagName("html")[0].classList.remove(class_name);
  },
  setFullScreen:function() {
    setTimeout(function(){
      const height = window.innerHeight;

      const headingEl = document.querySelector<HTMLElement>(".t-heading");
      const bottomEl = document.querySelector<HTMLElement>(".table-bottom");
      const table = document.querySelector<HTMLElement>("#leaderboard");

      const heading = headingEl ? Math.ceil(headingEl.offsetHeight) : 0;
      const tableBottom = bottomEl ? Math.ceil(bottomEl.offsetHeight) : 0;

      const tableContent = Math.ceil(height - (heading + tableBottom));

      const rowCount = table ? table.querySelectorAll("tr").length + 1 : 1;

      // apply height to all table td
      const tds = document.querySelectorAll<HTMLElement>(".table td");
      const cellHeight = Math.ceil(tableContent / rowCount) - 3.2;

      tds.forEach((td) => {
        td.style.height = `${cellHeight}px`;
      });
    },100);
  },
  convertAssessmentIds: function (ids){
    if(!ids) return null;
    var assessment_ids = ids.split(',');
    for(var idx = 0; idx < assessment_ids.length; idx++) {
      assessment_ids[idx] = parseInt(assessment_ids[idx]);
    }
    if(assessment_ids.length !== 1 && assessment_ids.length !== 7){
      alert('Missing the assessment_ids.');
      return null;
    }
    return assessment_ids;
  },
  formatAssessmentIds: function (assessment_ids){
    var ids= [];
    if(assessment_ids.length === 1){
      var temp = [];
      for(var idx = 0; idx < 7; idx++){
        temp[idx] = assessment_ids[0]
      }
      ids = temp;
    }else {
      ids = assessment_ids;
    }
    return ids;
  },
  getAssessmentIdsToArray: function (assessment_ids_arrays){
    var assessment_ids = [];
    for(var idx = 0; idx < 7; idx++){
      assessment_ids[idx] = [];
      for(var jdx = 0; jdx < assessment_ids_arrays.length; jdx++){
        assessment_ids[idx][jdx] = assessment_ids_arrays[jdx][idx];
      }
    }
    return assessment_ids;
  },
  getAssetsFromS3Assets: function (originalURL){
    if (!originalURL) return '';
    var regionPrefix = 's3.us-west-1.amazonaws.com/';
    return originalURL.replace(regionPrefix, '');
  },
  updateLeaderboardAssets: function () {
    if (!Utility.isLeaderboardAssetsExpired()) {
      Utility.leaderboard_assets.leaderboard_logo_url && $('.leaderboardLogo').attr('src', Utility.leaderboard_assets.leaderboard_logo_url);
      Utility.leaderboard_assets.challenge_logo_url && $('.challengeLogo').attr('src', Utility.leaderboard_assets.challenge_logo_url);
      Utility.leaderboard_assets.lionheart_logo_url && $('.lionheartLogo').attr('src', Utility.leaderboard_assets.lionheart_logo_url);
      return;
    }
    console.log('call leaderboard logo');
    var leaderBoardsApi = 'https://strapi.f45training.com/leaderboards';
    Utility.callAPI(leaderBoardsApi, null, function (error, response) {
      Utility.leaderboard_assets.expired = moment().add(6, 'hours');
      var defaultLogo = '/assets/images/logo-th.png';
      var defaultChallengeLogo = '/assets/images/challenge.svg';
      var defaultLionheartLogo = '/assets/images/lionheart.svg';
      if (error) {
        // leaderboardLogo
        $('.leaderboardLogo').attr('src', defaultLogo);
        Utility.leaderboard_assets.leaderboard_logo_url = defaultLogo;

        // challengeLogo
        $('.challengeLogo').attr('src', defaultChallengeLogo);
        Utility.leaderboard_assets.challenge_logo_url = defaultChallengeLogo;

        // lionheartLogo
        $('.lionheartLogo').attr('src', defaultLionheartLogo);
        Utility.leaderboard_assets.lionheart_logo_url = defaultLionheartLogo;
      } else {
        // leaderboardLogo
        var logoURL = Utility.getAssetsFromS3Assets((response && response.header_logo && response.header_logo.url))
        Utility.leaderboard_assets.leaderboard_logo_url = logoURL || defaultLogo;
        $('.leaderboardLogo').attr('src', logoURL || defaultLogo);

        // challengeLogo
        var challengeLogoURL = Utility.getAssetsFromS3Assets((response && response.flair_challenge && response.flair_challenge.image && response.flair_challenge.image.url))
        Utility.leaderboard_assets.challenge_logo_url = challengeLogoURL || defaultChallengeLogo;
        $('.challengeLogo').attr('src', challengeLogoURL || defaultChallengeLogo);

        // lionheartLogo
        var lionheartLogo = Utility.getAssetsFromS3Assets((response && response.flair_lionheart && response.flair_lionheart.image && response.flair_lionheart.image.url))
        Utility.leaderboard_assets.lionheart_logo_url = lionheartLogo || defaultLionheartLogo;
        $('.lionheartLogo').attr('src', lionheartLogo || defaultLionheartLogo);
      }
    }, null);
  },
  convertToCdnUrl: function (url) {
    if (!url) return '';
    var origins = [
      'http://f45storage.s3.amazonaws.com',
      'https://f45storage.s3.amazonaws.com',
      'http://f45storage.s3-us-west-1.amazonaws.com',
      'https://f45storage.s3-us-west-1.amazonaws.com',
      'http://s3-us-west-1.amazonaws.com/f45storage',
      'https://s3-us-west-1.amazonaws.com/f45storage'
    ];
    try {
      for (var i = 0; i < origins.length; i++) {
        if (url.indexOf(origins[i]) !== -1) {
          return url.replace(origins[i], 'https://f45storage.cdn.f45.com');
        }
      }
    } catch (e) {
      return url;
    }

    return url
  }
};
