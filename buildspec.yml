version: 0.2
phases:
    post_build:
        commands:
            - aws s3 sync flat s3://$S3_BUCKET/flat --acl public-read --delete --cache-control max-age=3600,public
            - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_ID --paths "/flat/*"
            - echo Build completed on `date`
artifacts:
    files:
        - '**/*'
    base-directory: 'flat*'
    discard-paths: yes
